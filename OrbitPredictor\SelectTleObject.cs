﻿using GeoEngine.Core;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Windows.Forms;

namespace OrbitPredictor
{
    public partial class SelectTleObject : Form
    {
        public SelectTleObject()
        {
            InitializeComponent();
        }

        private List<Tle> allTle;
        /// <summary>
        /// 传入轨道数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="select"></param>
        public void SetData(List<Tle> data, List<Tle> select)
        {
            if (data != null)
            {
                allTle = data;
                var s = 0;
                //this.checkedListBox1.Items.AddRange(data.Select(t => t.Name).ToArray());
                for (var i = 0; i < data.Count; i++)
                {
                    this.checkedListBox1.Items.Add(data[i].Name);
                    if (select != null && select.Count > 0)
                    {
                        for (var j = 0; j < select.Count; j++)
                        {
                            if (data[i].NoradNumber == select[j].NoradNumber)
                            {
                                this.checkedListBox1.SetItemChecked(this.checkedListBox1.Items.Count - 1, true);
                                s++;
                            }
                        }
                    }
                }
                if (this.checkedListBox1.Items.Count == s)
                {
                    this.cbAll.Checked = true;
                }
            }
        }

        /// <summary>
        /// 完成选择
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            List<Tle> set = new List<Tle>();
            foreach (int i in this.checkedListBox1.CheckedIndices)
            {
                set.Add(allTle[i]);
            }
            if (set.Count > 0)
            {
                SeletTle(set);
            }
        }


        public delegate void SetSelectTle(List<Tle> dt);
        public event SetSelectTle SeletTle;

        private void cbAll_CheckedChanged(object sender, EventArgs e)
        {
            for (var i = 0; i < this.checkedListBox1.Items.Count; i++)
            {
                this.checkedListBox1.SetItemChecked(i, this.cbAll.Checked);
            }
        }
    }
}
