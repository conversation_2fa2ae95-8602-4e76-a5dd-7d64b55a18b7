﻿using SignalAnalyzer.DBUtils.Access;
using SignalAnalyzer.Objects;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SignalAnalyzer.DataFactory
{
    public class RCL673_5L
    {

        private string rpath = null;
        private string resolverFilePath = string.Empty;
        public RCL673_5L(string dbPath, bool isCompact)
        {
            if (isCompact)
            {
                resolverFilePath = AccessUtil.Compact(dbPath) + "; Persist Security Info=False; Jet OLEDB:Engine Type=5; Jet OLEDB:Database Password=69123528;";
            }
            else
            {
                rpath = AccessUtil.Backup(dbPath);
                resolverFilePath = $"Provider=Microsoft.Jet.OLEDB.4.0; Data Source={rpath}; Persist Security Info=False; Jet OLEDB:Engine Type=5; Jet OLEDB:Database Password=69123528;";
            }
        }

        /// <summary>
        /// 这里处理完成后是否需要保存经过压缩的文件
        /// </summary>
        public void Close(bool isCompact)
        {
            //AccessUtil.Compact完成后调用Delete
            AccessUtil.Delete(rpath, isCompact);
        }

        public List<RadarSignal> ReadData()
        {
            string sql = "SELECT DB_TG_RADAR.RadarNo AS RadarNo, DB_TG_RADAR.SynDate AS SynDate, DB_TG_RADAR.InterT AS InterTime, " +
                           "DB_PT_RFT.ELENAME AS RFTName, DB_TG_RADARRF.RFMin AS RFMin, DB_TG_RADARRF.RFMax AS RFMax, " +
                           "DB_TG_RADARIPC.MFInit AS MFInit, DB_TG_RADARIPC.MFFinal AS MFFinal," +
                           "DB_PT_PRIT.ELENAME AS PRITName, DB_TG_RADARPRI.PRIMin AS PRIMin, DB_TG_RADARPRI.PRIMax AS PRIMax, " +
                           "DB_PT_PWT.ELENAME AS PWTName, DB_TG_RADARPW.PWMin AS PWMin, DB_TG_RADARPW.PWMax AS PWMax, " +
                           "DB_PT_IPCT.ELENAME AS IPCTName, DB_TG_RADAR.InterAZ AS InterAZ, DB_TG_RADAR.InterPIT AS InterPIT, " +
                           "DB_TG_RADAR.StatnID AS StatnID " + 
                           "FROM DB_PT_RFT INNER JOIN (DB_PT_PWT INNER JOIN (DB_PT_PRIT INNER JOIN " +
                           "(DB_PT_IPCT INNER JOIN ((((" +
                           "DB_TG_RADAR INNER JOIN DB_TG_RADARPRI ON (DB_TG_RADAR.SynDate = DB_TG_RADARPRI.SynDate) AND (DB_TG_RADAR.RadarNo = DB_TG_RADARPRI.RadarNo)) " +
                           "LEFT JOIN DB_TG_RADARPW ON (DB_TG_RADAR.SynDate = DB_TG_RADARPW.SynDate) AND (DB_TG_RADAR.RadarNo = DB_TG_RADARPW.RadarNo)) " +
                           "LEFT JOIN DB_TG_RADARRF ON (DB_TG_RADAR.SynDate = DB_TG_RADARRF.SynDate) AND (DB_TG_RADAR.RadarNo = DB_TG_RADARRF.RadarNo)) " +
                           "LEFT JOIN DB_TG_RADARIPC ON (DB_TG_RADAR.SynDate = DB_TG_RADARIPC.SynDate) AND (DB_TG_RADAR.RadarNo=DB_TG_RADARIPC.RadarNo)) " +
                           "ON DB_PT_IPCT.DECCODE = DB_TG_RADAR.IPCT) ON DB_PT_PRIT.DECCODE = DB_TG_RADAR.PRIT) ON DB_PT_PWT.DECCODE = DB_TG_RADAR.PWT) ON DB_PT_RFT.DECCODE = DB_TG_RADAR.RFT ";
            
            List<RadarSignal> mdbList = new List<RadarSignal>();
            //声明一个数据连接
            using (OleDbConnection con = new OleDbConnection(resolverFilePath))
            {
                if (!AppContext.UseIPM)
                {
                    sql = $"{sql} WHERE DB_TG_RADAR.RFT > 0 AND DB_TG_RADAR.PWT > 0 AND DB_TG_RADAR.PRIT > 0 AND DB_TG_RADAR.IPCT > 0 AND DB_TG_RADAR.IPCT < 5 ORDER BY DB_TG_RADAR.SynDate;";
                }
                else
                {
                    sql = $"{sql} WHERE DB_TG_RADAR.RFT > 0 AND DB_TG_RADAR.PWT > 0 AND DB_TG_RADAR.PRIT > 0 AND DB_TG_RADAR.IPCT > 0 ORDER BY DB_TG_RADAR.SynDate;";
                }
                
                OleDbDataAdapter da = new OleDbDataAdapter(sql, con);
                DataTable dt1 = new DataTable();
                
                da.Fill(dt1);
                mdbList = Common.DataConvert.GetEntityFromDataTable<RadarSignal>(dt1);
                //时间升序
                mdbList.Sort((a, b) => Utilities.LocalTimeChange.DateTimeToLong(a.SynDate).CompareTo(Utilities.LocalTimeChange.DateTimeToLong(b.SynDate)));
                
                con.Close();
                
            }
            
            return mdbList;
        }


        /// <summary>
        /// 读取坐标
        /// </summary>
        /// <returns></returns>
        public double[] ReadStn(int statnID)
        {
            string sql2 = $"SELECT StatnID,Longitude,Latitude,Height FROM DB_CM_STNSTATUS WHERE StatnID={statnID}";
            using (OleDbConnection con = new OleDbConnection(resolverFilePath))
            {
                OleDbDataAdapter da2 = new OleDbDataAdapter(sql2, con);
                DataTable dt2 = new DataTable();
                da2.Fill(dt2);
                con.Close();

                var rows = dt2.Rows;
                if (rows.Count > 0)
                {
                    var lon = Convert.ToDouble(rows[0]["Longitude"]);
                    var lat = Convert.ToDouble(rows[0]["Latitude"]);
                    var alt = Convert.ToDouble(rows[0]["Height"]);
                    double[] site = new double[] { lat, lon, alt };
                    return site;
                }
                return null;
            }
        }
    }
}
