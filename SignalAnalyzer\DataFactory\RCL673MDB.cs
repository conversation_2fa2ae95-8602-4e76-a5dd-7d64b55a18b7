﻿using SignalAnalyzer.DBUtils.Access;
using SignalAnalyzer.Objects;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Linq;
using System.Text;

namespace SignalAnalyzer.DataFactory
{
    public class RCL673MDB
    {

        public delegate void SetProgressValue(int val);
        public event SetProgressValue ProgressValue;

        private string rpath = null;
        private string resolverFilePath = string.Empty;
        public RCL673MDB(string dbPath)
        {
            rpath = AccessUtil.Backup(dbPath);
            resolverFilePath = $"Provider=Microsoft.Jet.OLEDB.4.0; Data Source={rpath}; Persist Security Info=False; Jet OLEDB:Engine Type=5;";
        }

        /// <summary>
        /// 获取临时文件名称，包含扩展名
        /// </summary>
        /// <returns></returns>
        public string GetTempFileName()
        {
            return System.IO.Path.GetFileName(rpath);
        }

        /// <summary>
        /// 对处理文件进行压缩并保存
        /// </summary>
        /// <param name="path"></param>
        public void CompactSave(string path)
        {
            AccessUtil.Compact(rpath);
            AccessUtil.SaveAs(path);
        }

        /// <summary>
        /// 这里处理完成后是否需要保存经过压缩的文件
        /// </summary>
        public void Close()
        {
            AccessUtil.Delete(rpath);
            rpath = null;
        }

        /// <summary>
        /// 保存处理文件至指定位置
        /// </summary>
        /// <param name="path"></param>
        public void SaveAs(string path)
        {
            if (System.IO.File.Exists(rpath))
            {
                System.IO.File.Copy(rpath, path, true);
                var result = AccessUtil.SaveAs(path);
                if (result)
                    rpath = null;
            }
        }

        /// <summary>
        /// 获取数据的最初时间和最后时间
        /// </summary>
        /// <returns></returns>
        public Tuple<DateTime?, DateTime?> GetStartAndEndTimes()
        {
            using (OleDbConnection con = new OleDbConnection(resolverFilePath))
            {
                con.Open();
                var sql = "SELECT MAX(DB_TG_RADAR.SynDate) AS MaxTime, MIN(DB_TG_RADAR.SynDate) AS MinTime " +
                              "FROM DB_PT_RFT INNER JOIN (DB_PT_PWT INNER JOIN (DB_PT_PRIT INNER JOIN " +
                              "(DB_PT_IPCT INNER JOIN ((((" +
                              "DB_TG_RADAR INNER JOIN DB_TG_RADARPRI ON (DB_TG_RADAR.SynDate = DB_TG_RADARPRI.SynDate) AND (DB_TG_RADAR.RadarNo = DB_TG_RADARPRI.RadarNo)) " +
                              "LEFT JOIN DB_TG_RADARPW ON (DB_TG_RADAR.SynDate = DB_TG_RADARPW.SynDate) AND (DB_TG_RADAR.RadarNo = DB_TG_RADARPW.RadarNo)) " +
                              "LEFT JOIN DB_TG_RADARRF ON (DB_TG_RADAR.SynDate = DB_TG_RADARRF.SynDate) AND (DB_TG_RADAR.RadarNo = DB_TG_RADARRF.RadarNo)) " +
                              "LEFT JOIN DB_TG_RADARIPC ON (DB_TG_RADAR.SynDate = DB_TG_RADARIPC.SynDate) AND (DB_TG_RADAR.RadarNo=DB_TG_RADARIPC.RadarNo)) " +
                              "ON DB_PT_IPCT.DECCODE = DB_TG_RADAR.IPCT) ON DB_PT_PRIT.DECCODE = DB_TG_RADAR.PRIT) ON DB_PT_PWT.DECCODE = DB_TG_RADAR.PWT) ON DB_PT_RFT.DECCODE = DB_TG_RADAR.RFT ";

                using (OleDbCommand cmd = new OleDbCommand(sql, con))
                {
                    using (OleDbDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            DateTime? maxTime = dr["MaxTime"] is DateTime ? Convert.ToDateTime(dr["MaxTime"]) : (DateTime?)null;
                            DateTime? minTime = dr["MaxTime"] is DateTime ? Convert.ToDateTime(dr["MinTime"]) : (DateTime?)null;
                            return new Tuple<DateTime?, DateTime?>(maxTime, minTime);
                        }
                    }
                }
                con.Close();
            }
            return new Tuple<DateTime?, DateTime?>(null, null);
        }

        /// <summary>
        /// 删除时间范围外的数据
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        /// <returns></returns>
        public int DeleteByTimeRange(DateTime dtStart, DateTime dtEnd)
        {
            using (OleDbConnection con = new OleDbConnection(resolverFilePath))
            {
                con.Open();
                var tran = con.BeginTransaction();
                var ret = 0;
                ProgressValue(0);
                try
                {
                    var sql1 = "DELETE FROM DB_TG_RADARRF WHERE SynDate < ? OR SynDate > ?";
                    using (OleDbCommand cmd = new OleDbCommand(sql1, con, tran))
                    {
                        cmd.Parameters.Add(new OleDbParameter() { Value = dtStart });
                        cmd.Parameters.Add(new OleDbParameter() { Value = dtEnd });
                        ret += cmd.ExecuteNonQuery();

                        ProgressValue(20);

                        cmd.CommandText = "DELETE FROM DB_TG_RADARPW WHERE SynDate < ? OR SynDate > ?";
                        ret += cmd.ExecuteNonQuery();

                        ProgressValue(40);

                        cmd.CommandText = "DELETE FROM DB_TG_RADARPRI WHERE SynDate < ? OR SynDate > ?";
                        ret += cmd.ExecuteNonQuery();

                        ProgressValue(60);

                        cmd.CommandText = "DELETE FROM DB_TG_RADARIPC WHERE SynDate < ? OR SynDate > ?";
                        ret += cmd.ExecuteNonQuery();

                        ProgressValue(80);

                        cmd.CommandText = "DELETE FROM DB_TG_RADAR WHERE SynDate < ? OR SynDate > ?";
                        ret += cmd.ExecuteNonQuery();
                    }
                    
                    tran.Commit();

                    ProgressValue(100);
                }
                catch (OleDbException ex)
                {
                    tran.Rollback();
                    ret = 0;
                    ProgressValue(0);
                    throw ex;
                }
                finally
                {
                    con.Close();
                }
                return ret;
            }
        }

        /// <summary>
        /// 追加时间范围目标数据库不存在的数据
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        /// <param name="dataPath"></param>
        /// <returns></returns>
        public int AppendFromTimeRange(DateTime dtStart, DateTime dtEnd, string dataPath)
        {
            var ret = 0;
            var sourceConstr = $"Provider=Microsoft.Jet.OLEDB.4.0; Data Source={dataPath}; Persist Security Info=False; Jet OLEDB:Engine Type=5;";

            ProgressValue(0);

            using (OleDbConnection sourceCon = new OleDbConnection(sourceConstr))
            {
                sourceCon.Open();
                using (OleDbCommand cmd = new OleDbCommand())
                {
                    cmd.Connection = sourceCon;
                    cmd.Parameters.Add(new OleDbParameter() { Value = dtStart });
                    cmd.Parameters.Add(new OleDbParameter() { Value = dtEnd });
                    // 查询新表数据
                    cmd.CommandText = $@"SELECT * FROM DB_TG_RADARRF WHERE SynDate BETWEEN ? AND ?;";
                    OleDbDataAdapter daRF = new OleDbDataAdapter(cmd);
                    DataTable dtRF = new DataTable();
                    daRF.Fill(dtRF);
                    cmd.CommandText = $@"SELECT * FROM DB_TG_RADARPW WHERE SynDate BETWEEN ? AND ?;";
                    OleDbDataAdapter daPW = new OleDbDataAdapter(cmd);
                    DataTable dtPW = new DataTable();
                    daPW.Fill(dtPW);
                    cmd.CommandText = $@"SELECT * FROM DB_TG_RADARPRI WHERE SynDate BETWEEN ? AND ?;";
                    OleDbDataAdapter daPRI = new OleDbDataAdapter(cmd);
                    DataTable dtPRI = new DataTable();
                    daPRI.Fill(dtPRI);
                    cmd.CommandText = $@"SELECT * FROM DB_TG_RADARIPC WHERE SynDate BETWEEN ? AND ?;";
                    OleDbDataAdapter daIPC = new OleDbDataAdapter(cmd);
                    DataTable dtIPC = new DataTable();
                    daIPC.Fill(dtIPC);
                    cmd.CommandText = $@"SELECT * FROM DB_TG_RADAR WHERE SynDate BETWEEN ? AND ?;";
                    OleDbDataAdapter dar = new OleDbDataAdapter(cmd);
                    DataTable dtr = new DataTable();
                    dar.Fill(dtr);

                    ProgressValue(10);

                    using (OleDbConnection targetCon = new OleDbConnection(resolverFilePath))
                    {
                        targetCon.Open();
                        var tran = targetCon.BeginTransaction();
                        try
                        {
                            using (OleDbCommand cmd2 = new OleDbCommand())
                            {
                                cmd2.Connection = targetCon;
                                cmd2.Transaction = tran;
                                // 创建临时表
                                cmd2.CommandText = "SELECT * INTO DB_TG_RADARRF_TEMP FROM DB_TG_RADARRF WHERE 0;";
                                cmd2.ExecuteNonQuery();
                                cmd2.CommandText = "SELECT * INTO DB_TG_RADARPW_TEMP FROM DB_TG_RADARPW WHERE 0;";
                                cmd2.ExecuteNonQuery();
                                cmd2.CommandText = "SELECT * INTO DB_TG_RADARPRI_TEMP FROM DB_TG_RADARPRI WHERE 0;";
                                cmd2.ExecuteNonQuery();
                                cmd2.CommandText = "SELECT * INTO DB_TG_RADARIPC_TEMP FROM DB_TG_RADARIPC WHERE 0;";
                                cmd2.ExecuteNonQuery();
                                cmd2.CommandText = "SELECT * INTO DB_TG_RADAR_TEMP FROM DB_TG_RADAR WHERE 0;";
                                cmd2.ExecuteNonQuery();

                                ProgressValue(20);

                                // 数据插入到临时表
                                var RFCols = dtRF.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToArray();
                                cmd2.CommandText = $"INSERT INTO DB_TG_RADARRF_TEMP({string.Join(",", RFCols)}) VALUES({string.Join(",", RFCols.Select(c => $"@{c}").ToArray())});";
                                var RFRows = dtRF.Rows.Cast<DataRow>().Select(r => r.ItemArray).ToList();
                                foreach (var items in RFRows)
                                {
                                    cmd2.Parameters.Clear();
                                    foreach (var item in items)
                                    {
                                        cmd2.Parameters.Add(new OleDbParameter() { Value = item });
                                    }
                                    ret += cmd2.ExecuteNonQuery();
                                }

                                ProgressValue(30);

                                var PWCols = dtPW.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToArray();
                                cmd2.CommandText = $"INSERT INTO DB_TG_RADARPW_TEMP({string.Join(",", PWCols)}) VALUES({string.Join(",", PWCols.Select(c => $"@{c}").ToArray())});";
                                var PWRows = dtPW.Rows.Cast<DataRow>().Select(r => r.ItemArray).ToList();
                                foreach (var items in PWRows)
                                {
                                    cmd2.Parameters.Clear();
                                    foreach (var item in items)
                                    {
                                        cmd2.Parameters.Add(new OleDbParameter() { Value = item });
                                    }
                                    ret += cmd2.ExecuteNonQuery();
                                }

                                ProgressValue(40);

                                var PRICols = dtPRI.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToArray();
                                cmd2.CommandText = $"INSERT INTO DB_TG_RADARPRI_TEMP({string.Join(",", PRICols)}) VALUES({string.Join(",", PRICols.Select(c => $"@{c}").ToArray())});";
                                var PRIRows = dtPRI.Rows.Cast<DataRow>().Select(r => r.ItemArray).ToList();
                                foreach (var items in PRIRows)
                                {
                                    cmd2.Parameters.Clear();
                                    foreach (var item in items)
                                    {
                                        cmd2.Parameters.Add(new OleDbParameter() { Value = item });
                                    }
                                    ret += cmd2.ExecuteNonQuery();
                                }

                                ProgressValue(50);

                                var IPCCols = dtIPC.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToArray();
                                cmd2.CommandText = $"INSERT INTO DB_TG_RADARIPC_TEMP({string.Join(",", IPCCols)}) VALUES({string.Join(",", IPCCols.Select(c => $"@{c}").ToArray())});";
                                var IPCRows = dtIPC.Rows.Cast<DataRow>().Select(r => r.ItemArray).ToList();
                                foreach (var items in IPCRows)
                                {
                                    cmd2.Parameters.Clear();
                                    foreach (var item in items)
                                    {
                                        cmd2.Parameters.Add(new OleDbParameter() { Value = item });
                                    }
                                    ret += cmd2.ExecuteNonQuery();
                                }

                                ProgressValue(60);

                                var rCols = dtr.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToArray();
                                cmd2.CommandText = $"INSERT INTO DB_TG_RADAR_TEMP({string.Join(",", rCols)}) VALUES({string.Join(",", rCols.Select(c => $"@{c}").ToArray())});";
                                var rRows = dtr.Rows.Cast<DataRow>().Select(r => r.ItemArray).ToList();
                                foreach (var items in rRows)
                                {
                                    cmd2.Parameters.Clear();
                                    foreach (var item in items)
                                    {
                                        cmd2.Parameters.Add(new OleDbParameter() { Value = item });
                                    }
                                    ret += cmd2.ExecuteNonQuery();
                                }

                                ProgressValue(70);

                                // 把临时表位于目标表不存在的数据插入目标表
                                cmd2.Parameters.Clear();
                                cmd2.CommandText = $@"INSERT INTO DB_TG_RADAR SELECT * FROM DB_TG_RADAR_TEMP AS s WHERE NOT EXISTS (SELECT 1 FROM DB_TG_RADAR AS d WHERE d.RadarNo = s.RadarNo AND d.SynDate = s.SynDate);";
                                ret += cmd2.ExecuteNonQuery();
                                cmd2.CommandText = $@"INSERT INTO DB_TG_RADARRF SELECT * FROM DB_TG_RADARRF_TEMP AS s WHERE NOT EXISTS (SELECT 1 FROM DB_TG_RADARRF AS d WHERE d.RadarNo = s.RadarNo AND d.SynDate = s.SynDate AND d.RFNo = s.RFNo);";
                                ret += cmd2.ExecuteNonQuery();
                                cmd2.CommandText = $@"INSERT INTO DB_TG_RADARPW SELECT * FROM DB_TG_RADARPW_TEMP AS s WHERE NOT EXISTS (SELECT 1 FROM DB_TG_RADARPW AS d WHERE d.RadarNo = s.RadarNo AND d.SynDate = s.SynDate AND d.PWNo = s.PWNo);";
                                ret += cmd2.ExecuteNonQuery();
                                cmd2.CommandText = $@"INSERT INTO DB_TG_RADARPRI SELECT * FROM DB_TG_RADARPRI_TEMP AS s WHERE NOT EXISTS (SELECT 1 FROM DB_TG_RADARPRI AS d WHERE d.RadarNo = s.RadarNo AND d.SynDate = s.SynDate AND d.PRINo = s.PRINo);";
                                ret += cmd2.ExecuteNonQuery();
                                cmd2.CommandText = $@"INSERT INTO DB_TG_RADARIPC SELECT * FROM DB_TG_RADARIPC_TEMP AS s WHERE NOT EXISTS (SELECT 1 FROM DB_TG_RADARIPC AS d WHERE d.RadarNo = s.RadarNo AND d.SynDate = s.SynDate AND d.FreqNo = s.FreqNo);";
                                ret += cmd2.ExecuteNonQuery();

                                ProgressValue(80);

                                // 删除临时表
                                cmd2.CommandText = $@"DROP TABLE DB_TG_RADARRF_TEMP,DB_TG_RADARPW_TEMP,DB_TG_RADARPRI_TEMP,DB_TG_RADARIPC_TEMP,DB_TG_RADAR_TEMP;";
                                ret += cmd2.ExecuteNonQuery();

                                ProgressValue(90);

                            }
                            tran.Commit();

                            ProgressValue(100);
                        }
                        catch (OleDbException ex)
                        {
                            tran.Rollback();
                            ret = 0;
                            ProgressValue(0);
                            throw ex;
                        }
                        finally
                        {
                            targetCon.Close();
                        }
                    }
                }
                sourceCon.Close();
            }
            return ret;
        }
        


    }
}
