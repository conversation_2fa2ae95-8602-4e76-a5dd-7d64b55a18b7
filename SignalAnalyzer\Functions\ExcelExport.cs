﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Excel = Microsoft.Office.Interop.Excel;
using System.Windows.Forms;
using SignalAnalyzer.Common;
using System.IO;

namespace SignalAnalyzer.Functions
{
    public class ExcelExport
    {
        private static Excel.Application _xlApp;
        private static Excel.Workbooks _workbooks;
        private static Excel.Workbook _workbook;

        private static readonly object syncRoot = new object();

        public static bool StartStatus = false;

        private static bool IsOperation = false;
        private static string OpenFullName;

        public static bool IsSaveAs(out string fullName)
        {
            fullName = _workbook?.FullName;
            if (OpenFullName == null)
            {
                return false;
            }
            return !fullName.Equals(Path.GetFullPath(OpenFullName));
        }

        private static Excel.Application GetInstance()
        {
            if (_xlApp == null)
            {
                lock (syncRoot)
                {
                    if (_xlApp == null)
                    {
                        _xlApp = new Excel.Application();
                    }
                }
            }
            return _xlApp;
        }

        public static bool Create()
        {
            if (!IsOperation)
            {
                if (GetInstance() == null)
                {
                    throw new Exception("无法创建Excel对象，您的电脑可能未安装Excel");
                }
                return true;
            }
            return false;
        }
        
        private static void Wb_Deactivate()
        {
            StartStatus = false;
        }

        public static void Close()
        {
            if (_xlApp != null)
            {
                StartStatus = false;
                IsOperation = false;
                OpenFullName = null;
                _workbook.Deactivate -= Wb_Deactivate;
                _workbook = null;
                _workbooks.Close();
                _workbooks = null;
                _xlApp.Quit();
                System.Runtime.InteropServices.Marshal.ReleaseComObject(_xlApp);
                _xlApp = null;
                GC.Collect();//强行销毁 
            }
        }

        public static void Open(string path = null, bool isVisible = false)
        {
            if (!IsOperation)
            {
                StartStatus = true;
                IsOperation = true;
                _xlApp.Visible = isVisible;
                _workbooks = _xlApp.Workbooks;
                if (path == null)
                {
                    _workbook = _workbooks.Add(Excel.XlWBATemplate.xlWBATWorksheet);//新建一个工作表。 新工作表将成为活动工作表。
                }
                else
                {
                    _workbook = _workbooks.Open(path, null, true);  // 只读打开，只能另存为，避免修改源模板
                    OpenFullName = path;
                }
                _workbook.Deactivate += Wb_Deactivate;
            }
        }

        public static void Save(string path)
        {
            _workbook.RefreshAll();
            string saveAsPath = string.Empty;
            if (IsSaveAs(out saveAsPath))
            {
                _workbook.Save();
            }
            else
            {
                _workbook.Saved = true;//获取或设置一个值，该值指示工作簿自上次保存以来是否进行了更改
                _workbook.SaveCopyAs(path);  //fileSaved = true;将工作簿副本保存到文件中，但不修改内存中打开的工作簿  
            }
        }

        public static void WriteHeaders(DataGridView dgView, int sheet)
        {
            Excel.Worksheet worksheet = (Excel.Worksheet)_workbook.Worksheets[sheet];//取得sheet1
            var colIndex = 0;
            for (int i = 0; i < dgView.ColumnCount; i++)//遍历循环获取DataGridView标题
            {
                if (dgView.Columns[i].Visible)
                {
                    colIndex++;
                    worksheet.Cells[1, colIndex] = dgView.Columns[i].HeaderText; // worksheet.Cells[1, i + 1]表示工作簿第一行第i+1列，Columns[i].HeaderText表示第i列的表头
                    var format = dgView.Columns[i].DefaultCellStyle.Format;
                    if (!string.IsNullOrWhiteSpace(format))
                    {  // 匹配DataGridView格式
                        var head = (Excel.Range)worksheet.Cells[2, colIndex];
                        var tail = (Excel.Range)worksheet.Cells[dgView.Rows.Count + 1, colIndex];
                        worksheet.Range[head, tail].NumberFormat = DataConvert.CSharpFormatToExcelFormat(format);
                    }
                }
            }
        }

        public static void WriteHeaders(string[] header, int sheet)
        {
            Excel.Worksheet worksheet = (Excel.Worksheet)_workbook.Worksheets[sheet];//取得sheet1
            for (int i = 0; i < header.Length; i++)
            {
                worksheet.Cells[1, i + 1] = header[i];
            }
        }

        public static void WriteData(DataGridView dgView, int sheet, int startRow)
        {
            Excel.Worksheet worksheet = (Excel.Worksheet)_workbook.Worksheets[sheet];//取得sheet1
            var len = dgView.Rows.Count;
            for (int r = 0; r < len; r++)//这里表示数据的行标,dataGridView1.Rows.Count表示行数
            {
                var c = 0;
                for (int i = 0; i < dgView.ColumnCount; i++)//遍历r行的列数
                {
                    if (dgView.Rows[r].Cells[i].Visible)
                    {
                        c++;
                        worksheet.Cells[r + startRow, c] = dgView.Rows[r].Cells[i].Value;//Cells[r + 2, i + 1]表示工作簿从第二行开始第一行保存为表头了，dataGridView1.Rows[r].Cells[i].Value获取列的r行i值
                    }
                }
                //Application.DoEvents();//实时更新表格
            }
        }

        public static void WriteRow(int sheet, int row, int column, object value, string format = null)
        {
            Excel.Worksheet worksheet = (Excel.Worksheet)_workbook.Worksheets[sheet];
            worksheet.Cells[row, column] = value;
            if (!string.IsNullOrWhiteSpace(format))
            {
                worksheet.Cells[row, column].NumberFormat = DataConvert.CSharpFormatToExcelFormat(format);
            }
        }

        public static void HeaderStyle1(int sheet)
        {
            Excel.Worksheet worksheet = (Excel.Worksheet)_workbook.Worksheets[sheet];
            worksheet.UsedRange.Font.Name = "黑体";
        }

        public static void WorksheetStyle1(int sheet)
        {
            Excel.Worksheet worksheet = (Excel.Worksheet)_workbook.Worksheets[sheet];
            worksheet.Cells.Font.Name = "宋体";
            worksheet.Cells.Font.Size = 11;
            worksheet.Cells.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;  // 水平居中
        }
        
        public static void ColumnsAutoFit(int sheet)
        {
            Excel.Worksheet worksheet = (Excel.Worksheet)_workbook.Worksheets[sheet];
            worksheet.Columns.EntireColumn.AutoFit();//列宽自适应
        }
    }
}
