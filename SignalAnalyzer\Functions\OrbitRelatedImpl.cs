﻿using GeoEngine;
using GeoEngine.API;
using GeoEngine.Core;
using GeoEngine.Mode;
using SignalAnalyzer.Common;
using SignalAnalyzer.Objects;
using SignalAnalyzer.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;

namespace SignalAnalyzer.Functions
{
    public class OrbitRelatedImpl
    {
        /// <summary>
        /// 读取卫星信息
        /// </summary>
        /// <returns></returns>
        public static List<SatelliteBase> ReadSatelliteBaseInfo()
        {
            string sqlSat = "SELECT SatNo AS SatelliteNo,SatName AS SatelliteName,SatEnName AS SatelliteEnName,SatArea AS BelongingArea, SatLaunchTime AS SatelliteLaunchTime, SatFreq AS SatelliteFreq FROM DB_K_SATELLITE";
            DataSet setSat = SQLiteHelper.ExecuteDataSet(sqlSat);
            List<SatelliteBase> satList = DataConvert.GetEntityFromDataTable<SatelliteBase>(setSat.Tables[0]);

            return satList;
        }

        /// <summary>
        /// 读取卫星参数
        /// </summary>
        /// <returns></returns>
        public static List<SatelliteParam> ReadSatelliteParam()
        {
            string sqlRadar = "SELECT DB_K_RADAR.RadarNo AS RadarNo, DB_K_RADAR.RadarFreq AS RadarFreq," +
                            "DB_PT_RFT.ELENAME AS RFType, DB_K_RADAR.RFMin AS RFMin, DB_K_RADAR.RFMax AS RFMax, " +
                            "DB_PT_PRIT.ELENAME AS PRIType, DB_K_RADAR.PRIMin AS PRIMin, DB_K_RADAR.PRIMax AS PRIMax, " +
                            "DB_PT_PWT.ELENAME AS PWType, DB_K_RADAR.PWMin AS PWMin, DB_K_RADAR.PWMax AS PWMax " +
                            "FROM DB_PT_RFT RIGHT JOIN (DB_PT_PRIT RIGHT JOIN (DB_PT_PWT RIGHT JOIN DB_K_RADAR ON DB_PT_PWT.DECCODE = DB_K_RADAR.PWType) " + 
                            "ON DB_PT_PRIT.DECCODE = DB_K_RADAR.PRIType) ON DB_PT_RFT.DECCODE = DB_K_RADAR.RFType";
            DataSet setRadar = SQLiteHelper.ExecuteDataSet(sqlRadar);
            List<SatelliteParam> satParam = DataConvert.GetEntityFromDataTable<SatelliteParam>(setRadar.Tables[0]);
            
            return satParam;
        }



        /// <summary>
        /// 根据TLE生成轨道模型
        /// </summary>
        /// <param name="selectLine">选择的卫星根数</param>
        /// <returns></returns>
        public static List<Orbit> ForecastOrbitData(List<Tle> selectTle)
        {
            List<Orbit> orbitList = new List<Orbit>();

            foreach (var tle in selectTle)
            {
                try
                {
                    Orbit orbitSDP4 = new Orbit(tle);
                    orbitList.Add(orbitSDP4);
                }
                catch (Exception ex)
                {
                    NLogHepler.Error(ex.Message);
                }
            }

            return orbitList;
        }

        /// <summary>
        /// 根据相关信息预报轨道
        /// </summary>
        /// <param name="siteEquator">观测点</param>
        /// <param name="orbitList">时间内的TLE轨道</param>
        /// <param name="timeStart">周期开始时间</param>
        /// <param name="timeEnd">周期结束时间</param>
        /// <param name="satEle">卫星俯仰最小角度</param>
        /// <returns></returns>
        public static List<OrbitDataView> ForecastOrbitView(Site siteEquator, List<Orbit> orbitList, DateTime timeStart, DateTime timeEnd, decimal[] satEle)
        {
            List<OrbitDataView> dvList = new List<OrbitDataView>();

            try
            {
                List<OrbitSatellite> osatList = OrbitAnalysis.ForecastOrbitSatellite(siteEquator, orbitList, timeStart, timeEnd);
                osatList.ForEach(osat =>
                {
                    osat.InTime = osat.InTime.ToLocalTime();
                    osat.OverTime = osat.OverTime.ToLocalTime();
                    osat.OutTime = osat.OutTime.ToLocalTime();

                    OrbitDataView oView = DataConvert.Mapper<OrbitDataView, OrbitSatellite>(osat);
                    dvList.Add(oView);
                });
            }
            catch (DecayException dex)
            {
                throw new Exception(dex.SatelliteName + "轨道预报异常");
            }

            dvList.Sort((a, b) => a.InTime.CompareTo(b.InTime));

            // 筛选大于指定角度的结果
            if (satEle.Length > 0)
            {
                var min = satEle.Min();
                var max = satEle.Max();
                if (min == max)
                {
                    min = 0;
                    max = 90;
                }
                dvList = dvList.Where(a => Convert.ToDecimal(a.OverElevationDeg) > min && Convert.ToDecimal(a.OverElevationDeg) < max).ToList();
            }

            return dvList;
        }

        
        public static List<MapPoint> EtlOrbitPoint(Orbit orbit, DateTime timeStart, DateTime timeEnd)
        {
            List<CurrentOrbitSatellite> osList = OrbitAnalysis.DesignatedOrbitSatellite(orbit, timeStart, timeEnd);
            List<MapPoint> mpList = new List<MapPoint>();
            osList.ForEach(s =>
            {
                MapPoint mp = new MapPoint();
                mp.X = s.LongitudeDeg;
                mp.Y = s.LatitudeDeg;
                mp.H = s.Altitude;
                MapPoint mp2 = Translator.LonLatToMercator(mp);
                mpList.Add(mp2);
            });
            return mpList;
        }


        /// <summary>
        /// 计算成像点到星下点的距离
        /// </summary>
        /// <param name="sideAngle">侧视角</param>
        /// <param name="targetAlt">目标高度</param>
        /// <returns></returns>
        public static double CalImagingDistance(double sideAngle, double targetAlt)
        {
            double sinbt = Math.Sin(Globals.ToRadians(sideAngle)) / Globals.EarthEquatorRadius * (Globals.EarthEquatorRadius + targetAlt);
            double bt = 180 - Globals.ToDegrees(Math.Asin(sinbt));
            double len = 2 * Globals.EarthEquatorRadius * Math.Sin(Globals.ToRadians(180 - sideAngle - bt) / 2);
            return len;
        }

        /// <summary>
        /// 经纬度转成摩尔托坐标
        /// </summary>
        /// <param name="gt"></param>
        /// <returns></returns>
        public static MapPoint LonLatToMercator(GeoTime gt)
        {
            MapPoint mp = new MapPoint();
            mp.X = gt.LongitudeDeg;
            mp.Y = gt.LatitudeDeg;
            MapPoint mp_to = Translator.LonLatToMercator(mp);
            return mp_to;
        }
    }
}
