﻿using System;
using System.ComponentModel;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OrbitPredictor.Controls
{
    public partial class DateTimeInputBox : UserControl
    {
        private Control[] thisTextBox;

        public DateTimeInputBox()
        {
            InitializeComponent();
            thisTextBox = new Control[] { this.textBoxBottomBorder1, this.textBoxBottomBorder2, this.textBoxBottomBorder3, this.textBoxBottomBorder4, this.textBoxBottomBorder5, this.textBoxBottomBorder6 };
            foreach (Control c in this.Controls)
            {
                if (c.GetType().Equals(typeof(TextBoxBottomBorder)))
                {
                    c.Enter += new EventHandler(c_Enter);
                    c.KeyPress += new KeyPressEventHandler(c_KeyPress);
                    c.TextChanged += new EventHandler(c_TextChanged);
                    c.Validating += new CancelEventHandler(c_Validating);
                }
            }
        }

        /// <summary>
        /// 当文本输入到最大字数时跳到下个输入框
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void c_TextChanged(object sender, EventArgs e)
        {
            if (thisTextBox != null && ((TextBox)sender).TextLength == ((TextBox)sender).MaxLength)
            {
                for (var i = 0; i < thisTextBox.Length - 1; i++)
                {
                    if (sender == thisTextBox[i])
                    {
                        thisTextBox[i + 1].Focus();
                    }
                }
            }
        }


        /// <summary>
        /// 控件激活后前面有年或者月没有填，把焦点放到年或者月
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void c_Enter(object sender, EventArgs e)
        {
            if (thisTextBox != null)
            {
                for (var i = 1; i < thisTextBox.Length; i++)
                {
                    if (sender == thisTextBox[i])
                    {
                        if (string.IsNullOrWhiteSpace(thisTextBox[i - 1].Text))
                        {
                            thisTextBox[i - 1].Focus();
                        }
                    }
                }
            }
        }


        void c_Validating(object sender, CancelEventArgs e)
        {
            const string pattern = @"^\d{1}";
            string content = ((TextBox)sender).Text;
            if (!string.IsNullOrWhiteSpace(content))
            {
                if (!Regex.IsMatch(content, pattern))
                {
                    ((TextBox)sender).Clear();
                    //((TextBox)sender).Focus();
                    e.Cancel = true;
                }
            }
        }

        /// <summary>
        /// 只允许输入数字或退格键
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void c_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar != 8 && !Char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
            if (e.KeyChar == 8)
            {
                //输入退格键后如果文本框没有值跳到上一个输入框
                if (thisTextBox != null && ((TextBox)sender).TextLength == 0)
                {
                    for (var i = 1; i < thisTextBox.Length; i++)
                    {
                        if (sender == thisTextBox[i])
                        {
                            thisTextBox[i - 1].Focus();
                        }
                    }
                }
            }
        }

        private void TimeInputBox_BackColorChanged(object sender, EventArgs e)
        {
            foreach (Control c in this.Controls)
            {
                c.BackColor = this.BackColor;
            }
        }

        private void TimeInputBox_FontChanged(object sender, EventArgs e)
        {
            foreach (Control c in this.Controls)
            {
                c.Font = this.Font;
            }
        }

        public override String Text
        {
            get
            {
                if (this.textBoxBottomBorder1.Text.Trim().Length > 0 && this.textBoxBottomBorder2.Text.Trim().Length > 0 && this.textBoxBottomBorder3.Text.Trim().Length > 0 && this.textBoxBottomBorder4.Text.Trim().Length > 0 && this.textBoxBottomBorder5.Text.Trim().Length > 0 && this.textBoxBottomBorder6.Text.Trim().Length > 0)
                    return $"{ this.textBoxBottomBorder1.Text }-{ this.textBoxBottomBorder2.Text }-{ this.textBoxBottomBorder3.Text } { this.textBoxBottomBorder4.Text }:{ this.textBoxBottomBorder5.Text }:{ this.textBoxBottomBorder6.Text }";
                return null;
            }
            //set
            //{
            //    string year = string.Empty, month = string.Empty, day = string.Empty, hour = string.Empty, minute = string.Empty, second = string.Empty;
            //    try
            //    {
            //        DateTime textDT = Convert.ToDateTime(value);
            //        year = textDT.Year.ToString();
            //        month = textDT.Month.ToString().PadLeft(2, '0');
            //        day = textDT.Day.ToString().PadLeft(2, '0');
            //        hour = textDT.Hour.ToString().PadLeft(2, '0');
            //        minute = textDT.Minute.ToString().PadLeft(2, '0');
            //        second = textDT.Second.ToString().PadLeft(2, '0');
            //    }
            //    catch { }
            //    this.textBoxBottomBorder1.Text = year;
            //    this.textBoxBottomBorder2.Text = month;
            //    this.textBoxBottomBorder3.Text = day;
            //    this.textBoxBottomBorder4.Text = hour;
            //    this.textBoxBottomBorder5.Text = minute;
            //    this.textBoxBottomBorder6.Text = second;
            //}
        }

        public DateTime DateTimeValue
        {
            get
            {
                DateTime returnDT;
                string dtStr = $"{ this.textBoxBottomBorder1.Text }-{ this.textBoxBottomBorder2.Text }-{ this.textBoxBottomBorder3.Text } { this.textBoxBottomBorder4.Text }:{ this.textBoxBottomBorder5.Text }:{ this.textBoxBottomBorder6.Text }";
                DateTime.TryParse(dtStr, out returnDT);
                return returnDT;
            }
            set
            {
                if (value != null)
                {
                    string year = value.Year.ToString();
                    string month = value.Month.ToString().PadLeft(2, '0');
                    string day = value.Day.ToString().PadLeft(2, '0');
                    string hour = value.Hour.ToString().PadLeft(2, '0');
                    string minute = value.Minute.ToString().PadLeft(2, '0');
                    string second = value.Second.ToString().PadLeft(2, '0');
                    this.textBoxBottomBorder1.Text = year;
                    this.textBoxBottomBorder2.Text = month;
                    this.textBoxBottomBorder3.Text = day;
                    this.textBoxBottomBorder4.Text = hour;
                    this.textBoxBottomBorder5.Text = minute;
                    this.textBoxBottomBorder6.Text = second;
                }
            }
        }
    }
}
