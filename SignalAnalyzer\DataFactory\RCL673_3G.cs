﻿using SignalAnalyzer.Objects;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SignalAnalyzer.DataFactory
{
    public class RCL673_3G
    {

        private static readonly byte[] makerHeader = new byte[] { 0x5A, 0x5A, 0x5A, 0x5A, 0, 0, 0x13, 0x3C };
        private const int rdwFrameLength = 512;
        private string resolverFilePath = string.Empty;
        public RCL673_3G(string dbPath)
        {
            resolverFilePath = dbPath;
        }

        public List<RadarSignalCollect> ReadData()
        {
            List<RadarSignalCollect> mdbList = new List<RadarSignalCollect>();
            using (var mmf = MemoryMappedFile.CreateFromFile(resolverFilePath, FileMode.Open))
            using (var stream = mmf.CreateViewStream())
            using (BinaryReader rdwFileReader = new BinaryReader(stream)) //File.Open(resolverFilePath, FileMode.Open, FileAccess.Read, FileShare.Read)
            {
                var brLen = rdwFileReader.BaseStream.Length;
                var i = 0L;
                while (i < brLen)
                {
                    try
                    {
                        var headerTemp = rdwFileReader.ReadBytes(8);
                        i += 8;
                        if (makerHeader.SequenceEqual(headerTemp))
                        {
                            if (mdbList.Count == 12443)
                            {
                                Console.Write(i);
                            }
                            var signal = RDWFrame(rdwFileReader, rdwFrameLength);
                            if (signal != null)
                            {
                                mdbList.Add(signal);
                            }
                            i = i + rdwFrameLength;
                        }
                    }
                    catch (Exception ex)
                    {
                        if (MessageBox.Show("RDW文件第" + i + "帧数据格式不正确\n" + ex.Message + "\n\n继续读取请点是，停止请点否\n\n\n", "警告", MessageBoxButtons.YesNo, MessageBoxIcon.Error) == System.Windows.Forms.DialogResult.Yes)
                            continue;
                        else
                            break;
                    }
                }
            }
            return mdbList;
        }

        /// <summary>
        /// 解析帧数据
        /// </summary>
        /// <param name="rdwFrameData"></param>
        /// <param name="frameLength"></param>
        /// <returns></returns>
        private RadarSignalCollect RDWFrame(BinaryReader rdwFrameData, int frameLength)
        {
            var sectionData = rdwFrameData.ReadBytes(frameLength);
            var distData = sectionData.Skip(36).Distinct();
            if (distData.Count() == 1 && distData.ElementAt(0) == 0)
                return null;
            //检查帧头部

            RadarSignalCollect signal = new RadarSignalCollect();
            int frame = 0;
            //signal.RadarNo = ;
            var no = (int)BytesToLong(new byte[] { sectionData[0], sectionData[1] });
            frame = 36;
            signal.SynDate = RDWFrameDateTime(sectionData, frame);
            frame += 12;
            signal.InterTime = RDWFrameDateTime(sectionData, frame);
            frame += 12;
            signal.RFTName = GetRFType((int)BytesToLong(sectionData.Skip(frame).Take(2).ToArray()));
            frame += 2;
            var rfCount = (int)BytesToLong(sectionData.Skip(frame).Take(2).ToArray());
            frame += 2;
            var RFValArr = RDWFrameSplit(sectionData, frame, rfCount);
            signal.RFValue = string.Join("/", RFValArr);
            frame = RDWFrameLastIndex(sectionData, frame + RFValArr.Length * 4);
            frame += 4;
            signal.RFMax = (double)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) * 1e-2m);
            frame += 4;
            signal.RFMin = (double)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) * 1e-2m);
            frame += 4;
            
            signal.PRITName = GetPRIType((int)BytesToLong(sectionData.Skip(frame).Take(2).ToArray()));
            frame += 2;
            var priCount = (int)BytesToLong(sectionData.Skip(frame).Take(2).ToArray());
            frame += 2;
            var PRIValArr = RDWFrameSplit(sectionData, frame, priCount);
            signal.PRIValue = string.Join("/", PRIValArr);
            var skip = frame;
            frame = RDWFrameLastIndex(sectionData, frame + PRIValArr.Length * 4);
            var ifSkip = frame;
            while (ifSkip - skip < 256)
            {
                //出现256字节异常帧
                sectionData = sectionData.Concat(rdwFrameData.ReadBytes(frameLength)).ToArray();
                frame = RDWFrameLastIndex(sectionData, frame + 256);
                ifSkip = frame - 256;
            }
            var PRITemp = PRIFrameCheck(sectionData, frame);
            if (PRITemp == 4)
            {
                frame += 4;
            }
            frame += 4;
            signal.PRIMax = (double)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) * 1e-2m);
            frame += 4;
            signal.PRIMin = (double)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) * 1e-2m);
            frame += 4;
            signal.PWTName = GetPWType((int)BytesToLong(sectionData.Skip(frame).Take(2).ToArray()));
            frame += 2;
            var pwCount = (int)BytesToLong(sectionData.Skip(frame).Take(2).ToArray());
            frame += 2;
            var PWValArr = RDWFrameSplit(sectionData, frame, pwCount);
            signal.PWValue = string.Join("/", PWValArr);
            frame = RDWFrameLastIndex(sectionData, frame + PWValArr.Length * 4);
            frame += 4;
            signal.PWMax = (double)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) * 1e-2m);
            frame += 4;
            signal.PWMin = (double)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) * 1e-2m);
            frame += 4;
            signal.PA = (float)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) / 2m);
            frame += 4;
            signal.InterAZ = (double)(BytesToLong(sectionData.Skip(frame).Take(2).ToArray()) * 1e-1m);
            frame += 2;
            //角度散布误差
            frame += 2;
            signal.MFValue = (double)(BytesToLong(sectionData.Skip(frame).Take(4).ToArray()) * 1e-2m);
            frame += 4;
            signal.InterPIT = (double)(BytesToLong(sectionData.Skip(frame).Take(2).ToArray()) * 1e-1m);
            frame += 2;

            return signal;
        }


        /// <summary>
        /// 检查RDW帧 PRI值数量
        /// </summary>
        /// <param name="sourceData"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private int PRIFrameCheck(byte[] sourceData, int startIndex)
        {
            var i = startIndex;
            List<byte[]> tempBytes = new List<byte[]>();
            while (BitConverter.ToUInt32(sourceData, i) > 0 || !(tempBytes.Count > 4))
            {
                tempBytes.Add(sourceData.Skip(i).Take(4).ToArray());
                i += 4;
            }
            var pwCount1 = (int)BytesToLong(tempBytes[3].Skip(2).ToArray());
            var pwCount2 = (int)BytesToLong(tempBytes[4].Skip(2).ToArray());
            if (tempBytes.Count == pwCount1 + 4)
            {
                return 3;
            }
            else if (tempBytes.Count == pwCount2 + 5)
            {
                return 4;
            }
            return tempBytes.Count;
        }

        /// <summary>
        /// RDW帧数组转换成整数
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private long BytesToLong(byte[] value)
        {
            long val = 0;
            if (value?.Length > 0)
            {
                var len = value.Length;
                var len_c = len - 1;
                if (len_c <= 8) //long类型位宽64
                {
                    for (var i = 0; i < len; i++)
                    {
                        val += value[i] << (8 * (len_c - i)); // * (int)Math.Pow(256, len - i - 1);
                    }
                }
            }
            return val;
        }

        /// <summary>
        /// 返回RDW帧空帧的最后索引值
        /// </summary>
        /// <param name="sourceData"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private int RDWFrameLastIndex(byte[] sourceData, int startIndex)
        {
            var i = startIndex;
            while (BitConverter.ToUInt32(sourceData, i) == 0)
            {
                i += 4;
            }
            return i;
        }

        /// <summary>
        /// RDW帧片段分割
        /// </summary>
        /// <param name="value">源数据</param>
        /// <param name="startIndex">开始索引</param>
        /// <param name="count">分片总数</param>
        /// <returns></returns>
        private double[] RDWFrameSplit(byte[] sourceData, int startIndex, int count)
        {
            List<double> splitArr = new List<double>();
            var i = startIndex;
            //var len = startIndex + count * 4;
            for (var l = 0; l < count; l++)
            {
                if (i + 4 < sourceData.Length)
                {
                    long val = BytesToLong(sourceData.Skip(i).Take(4).ToArray());
                    splitArr.Add(val * 1e-2);
                }
                i += 4;
            }
            return splitArr.ToArray();
        }

        /// <summary>
        /// RDW帧转时间
        /// </summary>
        /// <param name="sourceData">RDW帧时间数据12bit</param>
        /// <param name="startIndex">开始索引</param>
        /// <returns></returns>
        private DateTime RDWFrameDateTime(byte[] sourceData, int startIndex)
        {
            if (sourceData?.Length > startIndex + 12)
            {
                var year = 2000 + (int)BytesToLong(sourceData.Skip(startIndex).Take(2).ToArray());
                var days = sourceData[startIndex + 3] + (sourceData[startIndex + 2] & 0x80) / 128;
                var hours = sourceData[startIndex + 4] & 0x7F;
                var minutes = sourceData[startIndex + 5];
                var seconds = (int)BytesToLong(sourceData.Skip(startIndex + 6).Take(2).ToArray());
                //var milliseconds = (Convert.ToInt32(sourceData[55]) * 256 * 256 * 256 + Convert.ToInt32(sourceData[54]) * 256 * 256 + Convert.ToInt32(sourceData[53]) * 256 + sourceData[52]) * 6.4 * 1e-6;
                var milliseconds = BytesToLong(sourceData.Skip(startIndex + 8).Take(4).ToArray()) * 1e-6;
                return new DateTime(year, 1, 1).AddDays(days - 1).AddHours(hours).AddMinutes(minutes).AddSeconds(seconds).AddMilliseconds(milliseconds);
            }
            throw new ArgumentException("传入的RDW帧时间格式不正确");
        }

        private string GetPWType(int type)
        {
            switch (type)
            {
                case 1:
                    return "固定";

                case 6:
                    return "双脉冲";
                case 7:
                    return "多脉冲";

                default:
                    return "NULL";
            }
        }


        private string GetPRIType(int type)
        {
            switch (type)
            {
                case 1:
                    return "固定";
                case 2:
                    return "参差";

                case 4:
                    return "抖动";

                case 7:
                    return "组变";

                default:
                    return "NULL";
            }
        }



        private string GetRFType(int type)
        {
            switch (type)
            {
                case 1:
                    return "固定射频";

                case 3:
                    return "频率分集";

                case 5:
                    return "射频脉间捷变";
                case 6:
                    return "射频脉组捷变";

                default:
                    return "NULL";
            }
        }
    }

    enum PWType
    {
        PWF,
        PWS,
        PWC,
        DUW,
        MUI,
        UNW,
        NULL
    }


    enum PRIType
    {
        FXI,
        STI,
        
        JTI,
        
        AGI,
        NULL
    }
    
}
