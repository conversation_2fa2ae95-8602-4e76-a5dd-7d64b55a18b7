﻿using GeoEngine.API;
using SignalAnalyzer.Common;

namespace OrbitPredictor
{

    public class OrbitDataView : OrbitSatellite
    {
        public string BelongingArea { set; get; }  // 所属区域或国家
        
        // 轨道走向
        public OrbitTrendType OrbitTrend
        {
            get
            {
                double satLatDiff = base.InSubstar.Latitude - base.OutSubstar.Latitude;
                if (satLatDiff < 0) // 上行
                {
                    if (base.InAzimuthDeg < base.OverAzimuthDeg && base.OverAzimuthDeg < base.OutAzimuthDeg)
                    {
                        return OrbitTrendType.eRightUp;
                    }
                    else if (base.InAzimuthDeg > base.OverAzimuthDeg && base.OverAzimuthDeg > base.OutAzimuthDeg)
                    {
                        return OrbitTrendType.eLeftUp;
                    }
                    else if (base.InAzimuthDeg > 0 && base.OutAzimuthDeg > 270 && base.OutAzimuthDeg < 360)
                    {
                        return OrbitTrendType.eLeftUp;
                    }
                }
                else if (satLatDiff > 0) // 下行
                {
                    if (base.InAzimuthDeg < base.OverAzimuthDeg && base.OverAzimuthDeg < base.OutAzimuthDeg)
                    {
                        return OrbitTrendType.eRightDown;
                    }
                    else if (base.InAzimuthDeg > base.OverAzimuthDeg && base.OverAzimuthDeg > base.OutAzimuthDeg)
                    {
                        return OrbitTrendType.eLeftDown;
                    }
                    else if (base.InAzimuthDeg > 0 && base.InAzimuthDeg < 90 && base.OutAzimuthDeg > 180)
                    {
                        return OrbitTrendType.eLeftDown;
                    }
                }
                return OrbitTrendType.eUnknown;
            }
        }


    }
}
