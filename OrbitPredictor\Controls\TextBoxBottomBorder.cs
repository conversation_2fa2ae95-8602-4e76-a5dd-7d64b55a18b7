﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace OrbitPredictor.Controls
{
    public class TextBoxBottomBorder : TextBox
    {
        public TextBoxBottomBorder()
        {
            //去掉边框
            this.BorderStyle = BorderStyle.None;
            // BackColor也可以自己设置
            this.BackColor = SystemColors.Control;
        }

        //截获消息，在原来的下边框位置在画一条线
        private int WM_PAINT = 0x000F;
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);

            if (m.Msg == WM_PAINT)
            {
                Pen pen = new Pen(Brushes.Black, 1.5f);
                using (Graphics g = this.CreateGraphics())
                {
                    g.DrawLine(pen, new Point(0, this.Size.Height - 1),
                        new Point(this.Size.Width, this.Size.Height - 1));
                }
            }
        }
        
    }

}
