﻿using GeoEngine;
using GeoEngine.API;
using GeoEngine.Core;
using GeoEngine.Mode;
using SignalAnalyzer.Common;
using SignalAnalyzer.Common.TimeUtils;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OrbitPredictor
{
    public partial class OrbitMain : Form
    {
        private void SetLogText(string log)
        {
            string temp = DateTime.Now.ToString() + "|" + log;
            if (this.appLog.InvokeRequired)
            {
                this.appLog.BeginInvoke(new MethodInvoker(delegate
                {
                    LogText(temp);
                }));
            }
            else
            {
                LogText(temp);
            }
        }

        private void LogText(string log)
        {
            this.appLog.AppendText($"{log}\r\n");
            if (this.appLog.Lines.Length > 100)
            {
                this.appLog.Lines = this.appLog.Lines.Skip(80).ToArray();
            }
        }


        public OrbitMain()
        {
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer 
                | ControlStyles.ResizeRedraw 
                | ControlStyles.AllPaintingInWmPaint, true);
            this.UpdateStyles();

            InitializeComponent();

            OrbitPredictor.Controls.ControlsHelper.DoubleBuffered(this.forecastDataView, true);

            IniHelper.Path = AppDomain.CurrentDomain.BaseDirectory + @"config/GeneralConfig.ini";

            this.tbLat.Text = IniHelper.GetIniKey("Site", "Lat");
            this.tbLon.Text = IniHelper.GetIniKey("Site", "Lon");
            this.tbAlt.Text = IniHelper.GetIniKey("Site", "Alt");
            this.cbUseUTC.Checked = Convert.ToBoolean(IniHelper.GetIniKey("Time", "Utc"));
            this.cbSkipTime.Checked = Convert.ToBoolean(IniHelper.GetIniKey("Time", "IsSkip"));
            var skipRange = IniHelper.GetIniKey("Time", "SkipRange").Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (skipRange.Length > 0)
            {
                this.skipRangeList.Items.AddRange(skipRange);
            }
            this.cbRgCloseL.Checked = Convert.ToBoolean(IniHelper.GetIniKey("Time", "RegionCloseLeft"));
            this.cbRgCloseR.Checked = Convert.ToBoolean(IniHelper.GetIniKey("Time", "RegionCloseRight"));
            this.nicSatEleMin.Value = Convert.ToDecimal(IniHelper.GetIniKey("Analysis Settings", "MinEle"));
            this.nicSatEleMax.Value = Convert.ToDecimal(IniHelper.GetIniKey("Analysis Settings", "MaxEle"));

            var now = DateTime.Now;
            this.timeStart.DateTimeValue = now.Date;
            this.timeEnd.DateTimeValue = now.Date.AddDays(1).AddMilliseconds(-1);
            this.SetTimerText(now.ToString());
        }

        private static Image mapInit = Properties.Resources.GDMap_05_A;

        /// <summary>
        /// 初始化一个计时器用于时钟显示
        /// </summary>
        private System.Timers.Timer timer = new System.Timers.Timer(1000);
        private void SetTimerText(string timeValue)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new MethodInvoker(delegate
                {
                    this.toolSynTime.Text = timeValue;
                }));
            }
            else
            {
                this.toolSynTime.Text = timeValue;
            }
        }

        private void OrbitMain_Load(object sender, EventArgs e)
        {
            this.SetLogText("欢迎使用" );
            
            MapNewSize();
            
            timer.Elapsed += (o, ee) =>
            {
                this.SetTimerText(ee.SignalTime.ToString());
            };
            timer.AutoReset = true;
            timer.Start();

            this.mapPanel.MouseWheel += MapPanel_MouseWheel;
            tabWidth = this.tabPage1.Width;
            tabHeight = this.tabPage1.Height;
        }


        private void OrbitMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            timer.Stop();
            timer.AutoReset = false;

            IniHelper.WriteIniKey("Site", "Lat", this.tbLat.Text);
            IniHelper.WriteIniKey("Site", "Lon", this.tbLon.Text);
            IniHelper.WriteIniKey("Site", "Alt", this.tbAlt.Text);
            IniHelper.WriteIniKey("Time", "Utc", this.cbUseUTC.Checked.ToString());
            IniHelper.WriteIniKey("Time", "IsSkip", this.cbSkipTime.Checked.ToString());
            IniHelper.WriteIniKey("Time", "SkipRange", string.Join(",", this.skipRangeList.Items.Cast<string>()));
            IniHelper.WriteIniKey("Time", "RegionCloseLeft", this.cbRgCloseL.Checked.ToString());
            IniHelper.WriteIniKey("Time", "RegionCloseRight", this.cbRgCloseR.Checked.ToString());
            IniHelper.WriteIniKey("Analysis Settings", "MinEle", this.nicSatEleMin.Value.ToString());
            IniHelper.WriteIniKey("Analysis Settings", "MaxEle", this.nicSatEleMax.Value.ToString());

            timer.Close();
            timer.Dispose();
        }

        private void OrbitMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (this.Owner != null && !this.Owner.IsDisposed)
            {
                this.Owner.Show();
            }
        }


        /// <summary>
        /// 页面大小变化时的工作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tabPage1_Resize(object sender, EventArgs e)
        {
            if (this.WindowState != FormWindowState.Minimized)
            {
                MapNewSize();
                LoadOrbit(pointList);

                tabWidth = this.tabPage1.ClientRectangle.Width;
                tabHeight = this.tabPage1.ClientRectangle.Height;
            }
        }
        
        //设置文本框的
        private void SetProgressVal(int val)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new MethodInvoker(delegate
                {
                    this.progressBarDisplay.Value = val;
                    this.percentageValue.Text = $"{ val }%";
                }));
            }
            else
            {
                this.progressBarDisplay.Value = val;
                this.percentageValue.Text = $"{ val }%";
            }
            Application.DoEvents();
            System.Threading.Thread.Sleep(100);
        }


        #region 地图相关

        private int tabWidth;
        private int tabHeight;
        private int xPos;
        private int yPos;
        private bool MoveFlag;
        private double WheelAs = 1; //滚动放大系数
        private void mapPanel_MouseDown(object sender, MouseEventArgs e)
        {
            var pb = sender as Control;
            pb.Cursor = Cursors.SizeAll;
            MoveFlag = true; // 已经按下
            xPos = e.X; // 当前x坐标
            yPos = e.Y; // 当前y坐标
        }

        private void mapPanel_MouseUp(object sender, MouseEventArgs e)
        {
            var pb = sender as Control;
            pb.Cursor = Cursors.Cross;
            MoveFlag = false;
        }

        private void mapPanel_MouseMove(object sender, MouseEventArgs e)
        {
            if (MoveFlag)
            {
                // 鼠标按下移动
                var left = this.mapPanel.Left + e.X - xPos;
                var top = this.mapPanel.Top + e.Y - yPos;
                if (left > 0)
                {
                    left = 0;
                }
                if (left < (int)(this.tabPage1.Height * 1.5) - this.mapPanel.Width)
                {
                    left = (int)(this.tabPage1.Height * 1.5) - this.mapPanel.Width;
                }
                if (top > 0)
                {
                    top = 0;
                }
                if (top < this.tabPage1.Height - this.mapPanel.Height)
                {
                    top = this.tabPage1.Height - this.mapPanel.Height;
                }
                this.mapPanel.Location = new Point(left, top);
                

                // 框选区域放大
            }
            else
            {
                var pb = sender as Control;
                MapPoint mp = Translator.ScreenToMercator(e.X, e.Y, pb.ClientRectangle.Width, pb.ClientRectangle.Height);
                MapPoint newMp = Translator.MercatorToLonLat(mp);
                this.coordinateText.Text = $"{ newMp.X:f6},{ newMp.Y:f6}";
            }
        }
                
        private void MapPanel_MouseWheel(object sender, MouseEventArgs e)
        {
            int i = e.Delta * SystemInformation.MouseWheelScrollLines / 5;
            if (this.mapPanel.Height + i < this.tabPage1.Height * 3)
            {
                this.mapPanel.Width += i;
                this.mapPanel.Height += i;
                this.mapPanel.Left -= i / 2;
                this.mapPanel.Top -= i / 2;
                if (this.mapPanel.Height < this.tabPage1.Height)
                {
                    this.mapPanel.Size = new Size((int)(this.tabPage1.Height * 1.5), this.tabPage1.Height);
                }
                if (this.mapPanel.Left > 0)
                {
                    this.mapPanel.Left = 0;
                }
                if (this.mapPanel.Top > 0)
                {
                    this.mapPanel.Top = 0;
                }
                WheelAs = this.mapPanel.Height * 1.0 / this.tabPage1.Height;
            }
        }

        private void mapPanel_Paint(object sender, PaintEventArgs e)
        {
            var pb = sender as Control;
            //DrawGrids(pb, e);
            Brush b = new SolidBrush(Color.Red);
            MapPoint mp = new MapPoint();
            mp.X = Convert.ToDouble(this.tbLon.Text);
            mp.Y = Convert.ToDouble(this.tbLat.Text);
            MapPoint mp2 = Translator.LonLatToMercator(mp);
            //double x = DataConvert.LongitudeToX(mp2.X, pb.ClientRectangle.Width);
            //double y = DataConvert.LatitudeToY(mp2.Y, pb.ClientRectangle.Height);
            MapPoint x_y = Translator.MercatorToScreen(mp2, pb.ClientRectangle.Width, pb.ClientRectangle.Height);
            e.Graphics.FillEllipse(b, (float)(x_y.X - 5), (float)(x_y.Y - 5), 10, 10);
        }

        /// <summary>
        /// 画经纬网格
        /// </summary>
        /// <param name="c"></param>
        /// <param name="e"></param>
        private void DrawGrids(Control c, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            Pen myPen = Pens.White;
            for (int i = 0; i < c.ClientRectangle.Width; i++)
            {
                g.DrawLine(myPen, new Point(i, 0), new Point(i, c.ClientRectangle.Bottom));
                i += (int)(c.ClientRectangle.Width / 360.0 * 30);
            }

            for (int j = 0; j < c.ClientRectangle.Height; j++)
            {
                g.DrawLine(myPen, new Point(0, j), new Point(c.ClientRectangle.Right, j));
                j += (int)(c.ClientRectangle.Height / 180.0 * 30);
            }
        }

        private void MapNewSize()
        {
            if (WheelAs == 1)
            {
                Size newSize = new Size((int)(this.tabPage1.Height * 1.5), this.tabPage1.Height);
                this.mapPanel.Size = newSize;
                this.mapPanel.BackgroundImage = new Bitmap(mapInit, this.mapPanel.ClientRectangle.Size);
                this.mapPanel.Location = new Point(this.mapPanel.Left, this.mapPanel.Top);
            }
            else
            {
                var mapW = (int)(this.tabPage1.Height * 1.5);
                var mapH = this.tabPage1.Height;
                if (mapW < tabWidth * WheelAs)
                {
                    mapW = (int)(tabHeight * 1.5 * WheelAs);
                }
                if(mapH < tabHeight * WheelAs)
                {
                    mapH = (int)(tabHeight * WheelAs);
                }
                this.mapPanel.Size = new Size(mapW, mapH);
                this.mapPanel.BackgroundImage = new Bitmap(mapInit, this.mapPanel.ClientRectangle.Size);

                var left = this.mapPanel.Left;
                var top = this.mapPanel.Top;
                if (left > 0)
                {
                    left = 0;
                }
                if (left < (int)(this.tabPage1.Height * 1.5) - this.mapPanel.Width)
                {
                    left = (int)(this.tabPage1.Height * 1.5) - this.mapPanel.Width;
                }
                if (top > 0)
                {
                    top = 0;
                }
                if (top < this.tabPage1.Height - this.mapPanel.Height)
                {
                    top = this.tabPage1.Height - this.mapPanel.Height;
                }

                this.mapPanel.Location = new Point(left, top);
            }
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }

        #endregion



        /// <summary>
        /// 加载的TLE
        /// </summary>
        private List<Tle> allTle = null;
        /// <summary>
        /// 选择的TLE名字
        /// </summary>
        private List<Tle> selectTle = null;
        /// <summary>
        /// TLE转成的卫星模型
        /// </summary>
        List<Orbit> orbitList = null;

        /// <summary>
        /// 是否使用UTC时间
        /// </summary>
        private bool useUTC = false;
        private void cbUseUTC_CheckedChanged(object sender, EventArgs e)
        {
            useUTC = this.cbUseUTC.Checked;
        }

        /// <summary>
        /// 预报时间是否区间内闭环
        /// </summary>
        private bool rgCloseL = false;
        private bool rgCloseR = false;
        private void cbRgCloseL_CheckedChanged(object sender, EventArgs e)
        {
            rgCloseL = this.cbRgCloseL.Checked;
        }
        private void cbRgCloseR_CheckedChanged(object sender, EventArgs e)
        {
            rgCloseR = this.cbRgCloseR.Checked;
        }

        /// <summary>
        /// 重置到最初状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReset_Click(object sender, EventArgs e)
        {
            selectTle = null;
            orbitList?.Clear();

            this.forecastDataView.DataSource = null;
            this.mapPanel.BackgroundImage = new Bitmap(mapInit, this.mapPanel.ClientRectangle.Size);

            this.SetLogText("已重置");
            MessageBox.Show("已重置，请重新打开TLE文件进行预报！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }


        /// <summary>
        /// 打开TLE文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOpenTle_Click(object sender, EventArgs e)
        {
            OpenFileDialog file = new OpenFileDialog();
            file.Filter = "TLE文件|*.tle;*.txt";
            if (file.ShowDialog() == DialogResult.OK)
            {
                var skip = false;
                while (true)
                {
                    try
                    {
                        if (file.FilterIndex == 1)
                        {
                            allTle = OrbitAnalysis.AnyTLEFormFile(file.FileName, skip);
                        }
                        // 其他格式类型
                        this.SetLogText("加载TLE轨道完成");
                        break;
                    }
                    catch (Exception ex)
                    {
                        this.SetLogText($"{ex.Message} 轨道格式错误");
                        DialogResult dr = MessageBox.Show("TLE轨道格式错误，是否继续？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information);
                        if (dr == DialogResult.No || dr == DialogResult.Cancel)
                        {
                            break;
                        }
                        skip = true;
                    }
                }
            }
        }


        /// <summary>
        /// 选择需要预测的目标
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelect_Click(object sender, EventArgs e)
        {
            SelectTleObject stObj = new SelectTleObject();
            stObj.StartPosition = FormStartPosition.CenterParent;
            stObj.SetData(allTle, selectTle);
            stObj.SeletTle += a =>
            {
                selectTle = a;
            };
            stObj.ShowDialog();
        }


        /// <summary>
        /// 根据设置的条件预测轨道
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnForecast_Click(object sender, EventArgs e)
        {
            if (selectTle != null)
            {
                this.SetLogText("正在预报");

                //
                this.SetProgressVal(0);

                orbitList = new List<Orbit>();
                foreach (var tle in selectTle)
                {
                    Orbit orbitSDP4 = new Orbit(tle);
                    orbitList.Add(orbitSDP4);
                }
                
                var lat = double.Parse(this.tbLat.Text);
                var lon = double.Parse(this.tbLon.Text);
                var alt = double.Parse(this.tbAlt.Text) / 1000;
                Site siteEquator = new Site(lat, lon, alt);

                // 周期时间
                var timeStart = this.timeStart.DateTimeValue;
                var timeEnd = this.timeEnd.DateTimeValue;
                var satEleMin = this.nicSatEleMin.Value;
                var satEleMax = this.nicSatEleMax.Value;

                //
                this.SetProgressVal(20);

                List<OrbitDataView> dvList = new List<OrbitDataView>();

                try
                {
                    List<OrbitSatellite> osatList = new List<OrbitSatellite>();
                    var skip = false;
                    if (this.cbSkipTime.Checked)
                    {
                        var tsLen = this.skipRangeList.Items.Count;
                        if (tsLen > 0)
                        {
                            var timeSpan = new TimeSpan[tsLen, 2];
                            for (var i = 0; i < tsLen; i++)
                            {
                                var range = this.skipRangeList.Items[i].ToString().Split('-');
                                timeSpan[i, 0] = TimeSpan.Parse(range[0]);
                                timeSpan[i, 1] = TimeSpan.Parse(range[1]);
                            }

                            //要排除的时间段进行排序
                            timeSpan = ArrayUtil<TimeSpan>.OrderBy(timeSpan);
                            List<TimePeriod> erList = new List<TimePeriod>();
                            var rowCount = timeSpan.GetLength(0);
                            for (var r = 0; r < rowCount; r++)
                            {
                                TimePeriod tp = new TimePeriod();
                                tp.StartTime = timeSpan[r, 0];
                                tp.EndTime = timeSpan[r, 1];
                                erList.Add(tp);
                            }
                            var utp = TimeCalculator.TimePeriodUnion(erList);
                            TimeSpan[,] newRange = new TimeSpan[utp.Count, 2];
                            for (var u = 0; u < utp.Count; u++)
                            {
                                newRange[u, 0] = utp[u].StartTime;
                                newRange[u, 1] = utp[u].EndTime;
                            }
                            
                            List<TimePeriod> timeRange = TimeCalculator.TimePeriodDiff(timeStart, timeEnd, newRange);
                            foreach (var tRange in timeRange)
                            {
                                osatList.AddRange(OrbitAnalysis.ForecastOrbitSatellite(siteEquator, 
                                                                                        orbitList, 
                                                                                        new DateTime(tRange.StartTime.Ticks).ToUniversalTime(), 
                                                                                        new DateTime(tRange.EndTime.Ticks).ToUniversalTime(), 
                                                                                        rgCloseL, 
                                                                                        rgCloseR));
                            }
                            skip = true;
                        }
                    }
                    if (!skip)
                    {
                        osatList.AddRange(OrbitAnalysis.ForecastOrbitSatellite(siteEquator, 
                                                                                orbitList, 
                                                                                timeStart.ToUniversalTime(), 
                                                                                timeEnd.ToUniversalTime(), 
                                                                                rgCloseL, 
                                                                                rgCloseR));
                    }

                    //
                    this.SetProgressVal(50);

                    osatList.ForEach(osat =>
                    {
                        if (!useUTC)
                        {
                            osat.InTime = osat.InTime.ToLocalTime();
                            osat.OverTime = osat.OverTime.ToLocalTime();
                            osat.OutTime = osat.OutTime.ToLocalTime();
                        }

                        OrbitDataView oView = DataConvert.Mapper<OrbitDataView, OrbitSatellite>(osat);
                        dvList.Add(oView);
                    });

                    //
                    this.SetProgressVal(80);

                }
                catch (DecayException dex)
                {
                    this.SetLogText($"{dex.SatelliteName} 轨道预报异常");
                    MessageBox.Show(dex.SatelliteName + "轨道预报异常", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                
                dvList.Sort((a, b) => a.InTime.CompareTo(b.InTime));

                //satEleMin = satEleMin < 0 ? 0 : (satEleMin > 90 ? 90 : satEleMin);
                //satEleMax = satEleMax > 90 ? 90 : (satEleMax < 0 ? 0 : satEleMax);
                if (satEleMin > satEleMax)
                {
                    var min = satEleMin;
                    satEleMin = satEleMax;
                    satEleMax = min;
                }
                // 筛选指定角度范围内的结果
                dvList = dvList.Where(a => Convert.ToDecimal(a.OverElevationDeg) > satEleMin && Convert.ToDecimal(a.OverElevationDeg) < satEleMax).ToList();

                this.SetProgressVal(100);
                this.BeginInvoke(new SetOrbitDataSource(this.OrbitDataSource), new object[] { dvList });

                this.SetLogText("预报完成");
                this.SetLogText($"{timeStart.ToLocalTime()}-{timeEnd.ToLocalTime()} 模拟目标：{selectTle.Count} 个，总通过：{dvList.Count} 次");
            }
            else
            {
                this.SetLogText("无目标");
                MessageBox.Show("无目标", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        
        /// <summary>
        /// 轨道预报信息显示
        /// </summary>
        /// <param name="dt"></param>
        private delegate void SetOrbitDataSource(List<OrbitDataView> dt);
        private void OrbitDataSource(List<OrbitDataView> dt)
        {
            this.forecastDataView.DataSource = dt;
            this.forecastDataView.Columns["SatelliteNo"].DisplayIndex = 0;
            this.forecastDataView.Columns["SatelliteNo"].HeaderText = "卫星编号";
            this.forecastDataView.Columns["SatelliteName"].DisplayIndex = 1;
            this.forecastDataView.Columns["SatelliteName"].HeaderText = "卫星名称";
            //this.forecastDataView.Columns["BelongingArea"].HeaderText = "所属区域或国家";
            this.forecastDataView.Columns["BelongingArea"].Visible = false;
            this.forecastDataView.Columns["InTime"].DisplayIndex = 2;
            this.forecastDataView.Columns["InTime"].HeaderText = "进站时间";
            this.forecastDataView.Columns["InTime"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.forecastDataView.Columns["OutTime"].DisplayIndex = 3;
            this.forecastDataView.Columns["OutTime"].HeaderText = "出站时间";
            this.forecastDataView.Columns["OutTime"].DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss";
            this.forecastDataView.Columns["InAzimuthDeg"].DisplayIndex = 4;
            this.forecastDataView.Columns["InAzimuthDeg"].HeaderText = "进站方位";
            this.forecastDataView.Columns["InAzimuthDeg"].DefaultCellStyle.Format = "f2";
            this.forecastDataView.Columns["InElevationDeg"].DisplayIndex = 5;
            this.forecastDataView.Columns["InElevationDeg"].HeaderText = "进站俯仰";
            this.forecastDataView.Columns["InElevationDeg"].DefaultCellStyle.Format = "f2";
            this.forecastDataView.Columns["OutAzimuthDeg"].DisplayIndex = 6;
            this.forecastDataView.Columns["OutAzimuthDeg"].HeaderText = "出站方位";
            this.forecastDataView.Columns["OutAzimuthDeg"].DefaultCellStyle.Format = "f2";
            this.forecastDataView.Columns["OutElevationDeg"].DisplayIndex = 7;
            this.forecastDataView.Columns["OutElevationDeg"].HeaderText = "出站俯仰";
            this.forecastDataView.Columns["OutElevationDeg"].DefaultCellStyle.Format = "f2";
            this.forecastDataView.Columns["OverTime"].DisplayIndex = 8;
            this.forecastDataView.Columns["OverTime"].HeaderText = "过顶时间";
            this.forecastDataView.Columns["OverTime"].DefaultCellStyle.Format = "yyyy-MM-dd HH:mm:ss";
            //this.forecastDataView.Columns["OverTime"].Visible = false;
            this.forecastDataView.Columns["OverAzimuthDeg"].DisplayIndex = 9;
            this.forecastDataView.Columns["OverAzimuthDeg"].HeaderText = "最近方位";
            this.forecastDataView.Columns["OverAzimuthDeg"].DefaultCellStyle.Format = "f2";
            this.forecastDataView.Columns["OverElevationDeg"].DisplayIndex = 10;
            this.forecastDataView.Columns["OverElevationDeg"].HeaderText = "最高俯仰";
            this.forecastDataView.Columns["OverElevationDeg"].DefaultCellStyle.Format = "f2";
            this.forecastDataView.Columns["OrbitTrend"].DisplayIndex = 11;
            this.forecastDataView.Columns["OrbitTrend"].HeaderText = "轨道走势";
            this.forecastDataView.Columns["InSubstar"].Visible = false;
            this.forecastDataView.Columns["OverSubstar"].Visible = false;
            this.forecastDataView.Columns["OutSubstar"].Visible = false;
            this.forecastDataView.Refresh();
        }

        private void forecastDataView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex == 1)
            {
                switch ((OrbitTrendType)e.Value)
                {
                    case OrbitTrendType.eLeftUp:
                        e.Value = "上行左视";
                        break;
                    case OrbitTrendType.eRightDown:
                        e.Value = "下行右视";
                        break;
                    case OrbitTrendType.eRightUp:
                        e.Value = "上行右视";
                        break;
                    case OrbitTrendType.eLeftDown:
                        e.Value = "下行左视";
                        break;
                    default:
                        e.Value = "未知";
                        break;
                }
            }
        }

        /// <summary>
        /// 鼠标按下选中行
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void forecastDataView_CellMouseDown(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right && e.RowIndex > -1 && e.ColumnIndex > -1)
            {
                var dgv = (sender as DataGridView);
                dgv.ClearSelection();
                dgv.Rows[e.RowIndex].Selected = true;
                dgv.CurrentCell = dgv.Rows[e.RowIndex].Cells[e.ColumnIndex];
            }
        }



        #region 加载轨道
        
        /// <summary>
        /// 加载的轨道轨迹
        /// </summary>
        private List<MapPoint> pointList = new List<MapPoint>();
        /// <summary>
        /// 加载轨道
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void etlOrbit_Click(object sender, EventArgs e)
        {
            pointList.Clear();
            DataGridViewRow row = this.forecastDataView.CurrentRow;
            OrbitDataView orbitView = ((List<OrbitDataView>)this.forecastDataView.DataSource)[row.Index];
            Orbit orbit = orbitList.Find(o => o.SatNoradId == orbitView.SatelliteNo);
            List<CurrentOrbitSatellite> osList = OrbitAnalysis.DesignatedOrbitSatellite(orbit, 
                                                    orbitView.InTime.ToUniversalTime(), 
                                                    orbitView.OutTime.ToUniversalTime());
            osList.ForEach(s =>
            {
                MapPoint mp = new MapPoint();
                mp.X = s.LongitudeDeg;
                mp.Y = s.LatitudeDeg;
                mp.H = s.Altitude;
                MapPoint mp2 = Translator.LonLatToMercator(mp);
                pointList.Add(mp2);
            });
            
            this.tabControl1.SelectedTab = tabControl1.TabPages[0];

            LoadOrbit(pointList);
        }

        /// <summary>
        /// 图上显示加载的轨道
        /// </summary>
        /// <param name="pointList"></param>
        private void LoadOrbit(List<MapPoint> pointList)
        {
            if (pointList.Count > 1)
            {
                this.mapPanel.Controls.Clear();
                List<PointF> path = new List<PointF>();
                foreach (MapPoint mp2 in pointList)
                {
                    MapPoint x_y = Translator.MercatorToScreen(mp2, this.mapPanel.ClientSize.Width, this.mapPanel.ClientSize.Height);
                    PointF point = new PointF((int)x_y.X, (int)x_y.Y);
                    path.Add(point);
                }
                PointF[] points = path.AsParallel().Where((p, i) => path.FindIndex(a => a.X == p.X && a.Y == p.Y) == i).ToArray();
                if (points.Length > 1)
                {
                    Pen p = new Pen(Color.Yellow, 1);
                    Bitmap bt = new Bitmap(mapInit, this.mapPanel.ClientRectangle.Size);
                    Graphics g = Graphics.FromImage(bt);
                    g.DrawLines(p, points);
                    g.Dispose();
                    this.mapPanel.BackgroundImage = bt;
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }

            }

        }

        #endregion


        private void rangeToList_Click(object sender, EventArgs e)
        {
            if (!this.timeRangeBox.Verification())
            {
                MessageBox.Show("指定的时间设置错误", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            this.skipRangeList.Items.Add(this.timeRangeBox.ToString());
            this.skipRangeList.Refresh();
        }

        private void rangeDel_Click(object sender, EventArgs e)
        {
            var item = this.skipRangeList.SelectedItem;
            this.skipRangeList.Items.Remove(item);
            this.skipRangeList.Refresh();
        }

        private void rangeClear_Click(object sender, EventArgs e)
        {
            this.skipRangeList.Items.Clear();
            this.skipRangeList.Refresh();
        }

        private void exportExcel_Click(object sender, EventArgs e)
        {
            SaveFileDialog sfile = new SaveFileDialog();
            //sfile.DefaultExt = "csv";
            sfile.Filter = "CSV文件|*.csv|文本文件|*.txt";
            var d1 = this.timeStart.DateTimeValue.Day;
            var d2 = this.timeEnd.DateTimeValue.Day;
            if (d2 - d1 > 0)
            {
                sfile.FileName = $"{d1}至{d2}日轨道预报结果";
            }
            else
            {
                sfile.FileName = $"{d1}日轨道预报结果";
            }
            sfile.CheckPathExists = true;
            sfile.RestoreDirectory = true;
            sfile.ShowDialog();
            sfile.AddExtension = true;

            string sp;
            if (sfile.FilterIndex == 1)
            {
                sp = ",";
            }
            else
            {
                sp = "\t";
            }

            using (var wirte = new StreamWriter(sfile.FileName, false, Encoding.Default))
            {
                var rows = this.forecastDataView.Rows;
                var cc = this.forecastDataView.ColumnCount;
                foreach (DataGridViewRow r in rows)
                {
                    var line = new string[12];
                    for (int i = 0; i < cc; i++)
                    {
                        if (r.Cells[i].Visible)
                        {
                            var idx = r.Cells[i].OwningColumn.DisplayIndex;
                            line[idx] = Convert.ToString(r.Cells[i].FormattedValue);
                        }
                    }
                    wirte.WriteLine(string.Join(sp, line.ToArray()));
                }
                wirte.Flush();
            }
            MessageBox.Show("导出完成", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void menuDataOption_Opening(object sender, System.ComponentModel.CancelEventArgs e)
        {
            if (this.forecastDataView.RowCount == 0)
            {
                e.Cancel = true;
            }
        }
    }
}
