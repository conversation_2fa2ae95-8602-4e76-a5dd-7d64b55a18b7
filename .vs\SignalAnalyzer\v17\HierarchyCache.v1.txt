﻿++解决方案 'SignalAnalyzer' ‎ (6 个项目，共 4 个)
i:{00000000-0000-0000-0000-000000000000}:SignalAnalyzer.sln
++SignalAnalyzer.DBUtils
i:{00000000-0000-0000-0000-000000000000}:SignalAnalyzer.DBUtils
i:{5597b644-386b-4c10-9643-c0264824f867}:
++Properties
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.dbutils\properties\
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\properties\
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\iremotecontect\properties\
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\properties\
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\properties\
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\properties\
++引用
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++Access
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.dbutils\access\
++MySQL
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.dbutils\mysql\
++packages.config
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.dbutils\packages.config
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\packages.config
++AssemblyInfo.cs
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.dbutils\properties\assemblyinfo.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\properties\assemblyinfo.cs
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\iremotecontect\properties\assemblyinfo.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\properties\assemblyinfo.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\properties\assemblyinfo.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\properties\assemblyinfo.cs
++JRO
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
++Microsoft.CSharp
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++MySql.Data
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
++System
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.ComponentModel
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
++System.ComponentModel.DataAnnotations
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Configuration
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Configuration.Install
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Core
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.Data
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.Data.DataSetExtensions
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.Drawing
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.Drawing.Design
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
++System.Management
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Transactions
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Xml
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.Xml.Linq
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++AccessUtil.cs
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.dbutils\access\accessutil.cs
++MySQLUtil.cs
i:{1a7f9a6b-f53f-4228-974a-15c5df9114dd}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.dbutils\mysql\mysqlutil.cs
++SignalAnalyzer.Common
i:{00000000-0000-0000-0000-000000000000}:SignalAnalyzer.Common
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
++Controls
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\
++ComboxCheckedList.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\comboxcheckedlist.cs
++PanelEnhanced.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\panelenhanced.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\panelenhanced.cs
++TextBoxBottomBorder.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\textboxbottomborder.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\textboxbottomborder.cs
++TileMap.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\tilemap.cs
++TileMap.Designer.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\tilemap.designer.cs
++TileMap.resx
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\tilemap.resx
++TimeInputBox.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\timeinputbox.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\timeinputbox.cs
++TimeUtils
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\timeutils\
++ArrayUtil.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\arrayutil.cs
++ColorUtil.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\colorutil.cs
++DataConvert.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\dataconvert.cs
++ExcelNumberFormat.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\excelnumberformat.cs
++IniHelper.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\inihelper.cs
++MathInfo.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\mathinfo.cs
++SimplePinYin.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\simplepinyin.cs
++System.Net.Http
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
++System.Windows.Forms
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++ComboxCheckedList.Designer.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\comboxcheckedlist.designer.cs
++ComboxCheckedList.resx
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\comboxcheckedlist.resx
++TimeInputBox.Designer.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\timeinputbox.designer.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\timeinputbox.designer.cs
++TimeInputBox.resx
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\controls\timeinputbox.resx
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\timeinputbox.resx
++TimeCalculator.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\timeutils\timecalculator.cs
++TimePeriod.cs
i:{e1fcbf9b-b2ae-4a8e-af98-5fee4c8ecf80}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer.common\timeutils\timeperiod.cs
++IRemoteContect
i:{00000000-0000-0000-0000-000000000000}:IRemoteContect
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++Message.cs
i:{8f237b5b-2836-4e11-b6f3-4bc13f0133c2}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\iremotecontect\message.cs
++SignalAnalyzer
i:{00000000-0000-0000-0000-000000000000}:SignalAnalyzer
++CommonUtils
i:{5597b644-386b-4c10-9643-c0264824f867}:
++EntityFramework
i:{5597b644-386b-4c10-9643-c0264824f867}:
++GeoEngine
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
++Microsoft.Office.Core
i:{5597b644-386b-4c10-9643-c0264824f867}:
++Microsoft.Office.Interop.Excel
i:{5597b644-386b-4c10-9643-c0264824f867}:
++NLog
i:{5597b644-386b-4c10-9643-c0264824f867}:
++OrbitPredictor
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{00000000-0000-0000-0000-000000000000}:OrbitPredictor
++PresentationCore
i:{5597b644-386b-4c10-9643-c0264824f867}:
++PresentationFramework
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Data.SQLite
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Data.SQLite.EF6
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Data.SQLite.Linq
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Deployment
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.IO.Compression
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Runtime.Remoting
i:{5597b644-386b-4c10-9643-c0264824f867}:
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++System.Windows.Forms.DataVisualization
i:{5597b644-386b-4c10-9643-c0264824f867}:
++System.Xaml
i:{5597b644-386b-4c10-9643-c0264824f867}:
++VBIDE
i:{5597b644-386b-4c10-9643-c0264824f867}:
++WindowsBase
i:{5597b644-386b-4c10-9643-c0264824f867}:
++WindowsFormsIntegration
i:{5597b644-386b-4c10-9643-c0264824f867}:
++DataFactory
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\datafactory\
++DBSource.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\datafactory\dbsource.cs
++RCL673_3G.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\datafactory\rcl673_3g.cs
++RCL673_4.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\datafactory\rcl673_4.cs
++RCL673_4G.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\datafactory\rcl673_4g.cs
++RCL673_5L.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\datafactory\rcl673_5l.cs
++RCL673MDB.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\datafactory\rcl673mdb.cs
++Functions
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\functions\
++ExcelExport.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\functions\excelexport.cs
++MyCharts.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\functions\mycharts.cs
++OrbitRelatedImpl.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\functions\orbitrelatedimpl.cs
++SignalSelectionImpl.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\functions\signalselectionimpl.cs
++Objects
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\
++OrbitDataView.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\orbitdataview.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\orbitdataview.cs
++RadarSatellite.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\radarsatellite.cs
++RadarSignal.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\radarsignal.cs
++RadarSignalCollect.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\radarsignalcollect.cs
++RadarSignalSubmit.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\radarsignalsubmit.cs
++RaderSignalCapture.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\radersignalcapture.cs
++SatelliteBase.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\satellitebase.cs
++SatelliteCast.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\satellitecast.cs
++SatelliteInfo.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\satelliteinfo.cs
++SatelliteParam.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\satelliteparam.cs
++SignalExcel.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\signalexcel.cs
++SiteEquator.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\objects\siteequator.cs
++Resources
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\resources\
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\resources\
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\
++GDMap_05_A.jpg
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\resources\gdmap_05_a.jpg
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\resources\gdmap_05_a.jpg
++Utilities
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\
++AsyncTask.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\asynctask.cs
++Digitizer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\digitizer.cs
++Extensions.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\extensions.cs
++LocalTimeChange.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\localtimechange.cs
++NLogHepler.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\nloghepler.cs
++SQLiteHelper.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\sqlitehelper.cs
++UlidGenerator.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\ulidgenerator.cs
++XmlHelper.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\utilities\xmlhelper.cs
++AddInfo.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\addinfo.cs
++AddInfo.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\addinfo.designer.cs
++AddInfo.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\addinfo.resx
++AddParam.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\addparam.cs
++App.config
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\app.config
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\app.config
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\app.config
++AppContext.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\appcontext.cs
++DataAnalysis.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\dataanalysis.cs
++DataAnalysis.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\dataanalysis.designer.cs
++DataAnalysis.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\dataanalysis.resx
++DatabaseManager.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\databasemanager.cs
++EditInfo.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\editinfo.cs
++EditParam.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\editparam.cs
++Form1.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\form1.cs
++icon.ico
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\icon.ico
++Program.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\program.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\program.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\program.cs
++RadarEdits.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\radaredits.cs
++ResourcesRegulator.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\resourcesregulator.cs
++SelectDataType.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selectdatatype.cs
++SelectSatellite.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selectsatellite.cs
++SelectTleObject.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selecttleobject.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\selecttleobject.cs
++ShowElevation.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\showelevation.cs
++SystemSetting.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\systemsetting.cs
++app.manifest
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\properties\app.manifest
++Resources.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\properties\resources.resx
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\properties\resources.resx
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\properties\resources.resx
++Settings.settings
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\properties\settings.settings
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\properties\settings.settings
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\properties\settings.settings
++AddParam.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\addparam.designer.cs
++AddParam.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\addparam.resx
++DatabaseManager.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\databasemanager.designer.cs
++DatabaseManager.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\databasemanager.resx
++EditInfo.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\editinfo.designer.cs
++EditInfo.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\editinfo.resx
++EditParam.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\editparam.designer.cs
++EditParam.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\editparam.resx
++Form1.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\form1.designer.cs
++Form1.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\form1.resx
++RadarEdits.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\radaredits.designer.cs
++RadarEdits.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\radaredits.resx
++SelectDataType.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selectdatatype.designer.cs
++SelectDataType.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selectdatatype.resx
++SelectSatellite.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selectsatellite.designer.cs
++SelectSatellite.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selectsatellite.resx
++SelectTleObject.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selecttleobject.designer.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\selecttleobject.designer.cs
++SelectTleObject.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\selecttleobject.resx
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\selecttleobject.resx
++ShowElevation.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\showelevation.designer.cs
++ShowElevation.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\showelevation.resx
++SystemSetting.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\systemsetting.designer.cs
++SystemSetting.resx
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\systemsetting.resx
++Resources.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\properties\resources.designer.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\properties\resources.designer.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\properties\resources.designer.cs
++Settings.Designer.cs
i:{5597b644-386b-4c10-9643-c0264824f867}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\signalanalyzer\properties\settings.designer.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\properties\settings.designer.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\properties\settings.designer.cs
++ControlsHelper.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\controlshelper.cs
++DateTimeInputBox.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\datetimeinputbox.cs
++favicon.ico
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\favicon.ico
++OrbitMain.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\orbitmain.cs
++DateTimeInputBox.Designer.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\datetimeinputbox.designer.cs
++DateTimeInputBox.resx
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\controls\datetimeinputbox.resx
++OrbitMain.Designer.cs
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\orbitmain.designer.cs
++OrbitMain.resx
i:{af6bf8c2-b74d-4fd2-8827-e3daf37277bf}:d:\工作\copy\v1.3 - copy - 副本 -123 - 副本 (2) - 副本\orbitpredictor\orbitmain.resx
++STKSimulator
i:{00000000-0000-0000-0000-000000000000}:STKSimulator
i:{bfb4733d-76e8-48a0-891d-fe29db48aa09}:STKSimulator
++DeductionAngle.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\deductionangle.cs
++DeductionObject.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\deductionobject.cs
++MsgConnect.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\msgconnect.cs
++RemoteObject.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\remoteobject.cs
++stk.ico
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\stk.ico
++STKEngineDemo.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\stkenginedemo.cs
++TLEListShow.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\tlelistshow.cs
++Utilities.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\utilities.cs
++AGI.STKGraphics
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++AGI.STKObjects
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++AGI.STKUtil
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++AGI.STKVgt
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++AGI.STKX
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++AxAGI.STKX
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++stdole
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:
++decreasedelta.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\decreasedelta.png
++home.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\home.png
++increasedelta.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\increasedelta.png
++pan.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\pan.png
++panpressed.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\panpressed.png
++pause.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\pause.png
++playforward.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\playforward.png
++playreverse.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\playreverse.png
++reset.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\reset.png
++stepforward.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\stepforward.png
++stepreverse.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\stepreverse.png
++zoom.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\zoom.png
++zoompressed.png
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\resources\zoompressed.png
++STKEngineDemo.Designer.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\stkenginedemo.designer.cs
++STKEngineDemo.resx
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\stkenginedemo.resx
++TLEListShow.Designer.cs
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\tlelistshow.designer.cs
++TLEListShow.resx
i:{788ef341-4c79-4fda-a4e8-b1737da67fb2}:d:\工作\copy\stksimulator\stksimulator\tlelistshow.resx
