﻿using System;
using System.Reflection;
using System.Windows.Forms;

namespace OrbitPredictor.Controls
{
    public class ControlsHelper
    {

        /// <summary>
        /// 给控件启用双缓冲
        /// </summary>
        /// <param name="control">DataGridView</param>
        /// <param name="setting"></param>
        public static void DoubleBuffered(Control control, bool setting)
        {
            Type dgvType = control.GetType();
            PropertyInfo pi = dgvType.GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic);
            pi.SetValue(control, setting, null);
        }


    }
}
