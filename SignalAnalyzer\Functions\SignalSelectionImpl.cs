﻿using GeoEngine;
using GeoEngine.Core;
using GeoEngine.Mode;
using SignalAnalyzer.Common;
using SignalAnalyzer.Objects;
using SignalAnalyzer.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SignalAnalyzer.Functions
{
    internal class SignalSelectionImpl
    {

        /// <summary>
        /// 进一步判断一批次里的重复周期，必须大于两条数据才调用此方法
        /// </summary>
        /// <param name="scaList"></param>
        /// <param name="ssList"></param>
        /// <returns></returns>
        public static void DeepPRI(ref List<List<RadarSignal>> scaList, List<RadarSignal> ssList)
        {
            List<RadarSignal> tempList = new List<RadarSignal>();
            tempList.Add(ssList[0]);
            var diff = ssList[1].PRIMax - ssList[0].PRIMax;
            var timeLen = (LocalTimeChange.DateTimeToLong(ssList[1].SynDate) - LocalTimeChange.DateTimeToLong(ssList[0].SynDate)) / 1000;
            for (var p = 1; p < ssList.Count; p++)
            {
                if (p + 1 < ssList.Count)
                {
                    var diff2 = ssList[p + 1].PRIMax - ssList[p].PRIMax;
                    var timeLen2 = (LocalTimeChange.DateTimeToLong(ssList[p + 1].SynDate) - LocalTimeChange.DateTimeToLong(ssList[p].SynDate)) / 1000;
                    //var sub1 = Math.Abs(diff / (timeLen > 0 ? timeLen : 1));
                    //var sub2 = Math.Abs(diff2 / (timeLen2 > 0 ? timeLen2 : 1));
                    if (Math.Abs(diff2 / (timeLen2 > 0 ? timeLen2 : 1) - diff / (timeLen > 0 ? timeLen : 1)) > AppContext.PRISpec)
                    {
                        if (Math.Abs(diff2 / (timeLen2 > 0 ? timeLen2 : 1)) > Math.Abs(diff / (timeLen > 0 ? timeLen : 1)))
                        {
                            tempList.Add(ssList[p]);
                            scaList.Add(tempList.ToList());
                            tempList.Clear();
                            ssList.RemoveRange(0, p + 1);
                            if (ssList.Count > 1)
                            {
                                DeepPRI(ref scaList, ssList);
                                return;
                            }
                            else
                            {
                                scaList.Add(ssList);
                                return;
                            }
                        }
                        else
                        {
                            scaList.Add(tempList.ToList());
                            tempList.Clear();
                        }
                    }
                    diff = diff2;
                    timeLen = timeLen2;
                }
                tempList.Add(ssList[p]);
            }
            scaList.Add(tempList.ToList());
            tempList.Clear();
        }

        /// <summary>
        /// 合计批次数据
        /// </summary>
        /// <param name="ssList"></param>
        /// <returns></returns>
        public static SignalExcel TotalSignalList(List<RadarSignal> ssList)
        {
            SignalExcel sExcel = new SignalExcel();
            sExcel.SynStartDate = ssList[0].SynDate.ToString("yyyy/MM/dd HH:mm:ss").Replace("-", "/");
            sExcel.SynEndDate = ssList[ssList.Count - 1].SynDate.ToString("yyyy/MM/dd HH:mm:ss").Replace("-", "/");
            sExcel.SynTimeDura = (int)(LocalTimeChange.DateTimeToLong(ssList[ssList.Count - 1].SynDate) - LocalTimeChange.DateTimeToLong(ssList[0].SynDate)) / 1000 + 1;
            sExcel.RFTName = ssList[0].RFTName;
            sExcel.RFValue = DataConvert.ORound((ssList.Max(s => s.RFMax) + ssList.Min(s => s.RFMin)) / 2);
            sExcel.PRITName = ssList[0].PRITName;
            List<RadarSignal> ifList = ssList.Where(s => s.PRIMax > 10).ToList();
            sExcel.PRIValue = ifList.Count > 0 ? ifList.Average(s => (s.PRIMax + s.PRIMin) / 2) : ssList.Average(s => (s.PRIMax + s.PRIMin) / 2); 
            sExcel.PWTName = ssList[0].PWTName;
            //sExcel.PWValue = ssList.Max(s => s.PWMax); 
            sExcel.IPCTName = ssList[0].IPCTName;
            sExcel.InterAZ = ssList[0].InterAZ;
            sExcel.InterPIT = ssList[0].InterPIT; 
            sExcel.PA = (int)ssList.Average(s => s.PA); // 
            sExcel.IPCTValue = Math.Abs(ssList[0].MFInit - ssList[0].MFFinal);
            return sExcel;
        }

        /// <summary>
        /// 自适应趋势与某个区间目标值
        /// </summary>
        /// <param name="soueceData"></param>
        /// <param name="targetValue"></param>
        /// <returns></returns>
        public static double[] MeanDeviation(List<double> soueceData, double targetValue)
        {
            var meanDeviation = soueceData.Average(d => Math.Abs(d - targetValue));
            return soueceData.Select(data =>
            {
                var deviation = data - targetValue;
                var adjustmentFactor = Math.Abs(deviation) / meanDeviation;
                return targetValue + deviation * (1 / (1 + adjustmentFactor));
            }).ToArray();
        }

        public static T NormalizeToRange<T>(T value, T oldMin, T oldMax, T newMin, T newMax) where T : struct, IComparable
        {
            dynamic val = value;
            dynamic oMin = oldMin;
            dynamic oMax = oldMax;
            dynamic nMin = newMin;
            dynamic nMax = newMax;
            double ratio = (val - oMin) / (double)(oMax - oMin);

            return (T)(nMin + ratio * (nMax - nMin));
        }
        
        public static IEnumerable<T> NormalizeToRange<T>(IEnumerable<T> data, T oldMin, T oldMax, T newMin, T newMax) where T : struct, IComparable
        {
            if (data == null) throw new ArgumentNullException(nameof(data));

            if (data.Count() == 1)
            {
                yield return NormalizeToRange(data.ElementAt(0), oldMin, oldMax, newMin, newMax);
            }

            dynamic oMin = oldMin;
            dynamic oMax = oldMax;
            dynamic nMin = newMin;
            dynamic nMax = newMax;
            double scale = (double)(nMax - nMin) / (double)(oMax - oMin);

            foreach (var item in data)
            {
                dynamic val = item;
                yield return (T)(nMin + (val - oMin) * scale);
            }
        }

        /// <summary>
        /// 重周降倍处理
        /// </summary>
        /// <param name="rsClone"></param>
        public static void Dedoubling(ref List<RadarSignal> rsClone)
        {
            var rsLen = rsClone.Count;
            if (rsLen > 1) // 保证记录2条以上才处理
            {
                for (int r = 1; r < rsLen; r++)
                {
                    var rs1 = rsClone[r - 1];
                    var pri100 = rs1.PRITName == "固定" && rs1.PRIMin > 100;
                    if (pri100)
                    {

                    }
                }
            }
        }

        public static void Dedoubling(ref List<RadarSignal> rsClone, Orbit orbit)
        {
            var rsLen = rsClone.Count;
            if (rsLen > 1) // 保证记录2条以上才处理
            {
                for (int r = 1; r < rsLen; r++)
                {
                    var rs1 = rsClone[r - 1];
                    // 斜距不会小于高度
                    EciTime et1 = orbit.GetPosition(rs1.SynDate.ToUniversalTime());
                    GeoTime gt1 = new GeoTime(et1);
                    var altState1 = CalObliqueDistance(rs1.PRIMax, rs1.PWMax, 20) > gt1.Altitude;
                    var rs2 = rsClone[r];
                    if (r > rsLen - 2)
                    {
                        // 最后一次比较
                        if (altState1 
                            && Math.Round(rs1.PRIMax * 2) <= Math.Round(rs2.PRIMax) + 1 
                            && LocalTimeChange.DateTimeToLong(rs2.SynDate) - LocalTimeChange.DateTimeToLong(rs1.SynDate) < TimeSpec(AppContext.TimeSpec))
                        {
                            rs2.PRIMax = rs2.PRIMax / Math.Round(rs2.PRIMax / rs1.PRIMax);
                            rs2.PRIMin = rs2.PRIMin / Math.Round(rs2.PRIMin / rs1.PRIMin);
                        }
                        break;
                    }
                    else
                    {
                        var rs3 = rsClone[r + 1];
                        EciTime et3 = orbit.GetPosition(rs1.SynDate.ToUniversalTime());
                        GeoTime gt3 = new GeoTime(et3);
                        var altState3 = CalObliqueDistance(rs3.PRIMax, rs3.PWMax, 20) > gt3.Altitude;
                        if (altState1 && altState3 
                            && Math.Round(rs1.PRIMax * 2) <= Math.Round(rs2.PRIMax) + 1
                            && Math.Round(rs3.PRIMax * 2) <= Math.Round(rs2.PRIMax) + 1)
                        {
                            // 比较两边的倍率谁更接近整数
                            if (LocalTimeChange.DateTimeToLong(rs2.SynDate) - LocalTimeChange.DateTimeToLong(rs1.SynDate) < TimeSpec(AppContext.TimeSpec)
                                && LocalTimeChange.DateTimeToLong(rs3.SynDate) - LocalTimeChange.DateTimeToLong(rs2.SynDate) < TimeSpec(AppContext.TimeSpec))
                            {
                                var a = rs2.PRIMax / rs1.PRIMax;
                                var b = rs2.PRIMax / rs3.PRIMax;
                                if (Math.Abs(Math.Round(a) - a) - Math.Abs(Math.Round(b) - b) > 0)
                                {
                                    rs2.PRIMax = rs2.PRIMax / Math.Round(b);
                                    rs2.PRIMin = rs2.PRIMin / Math.Round(b);
                                }
                                else
                                {
                                    rs2.PRIMax = rs2.PRIMax / Math.Round(a);
                                    rs2.PRIMin = rs2.PRIMin / Math.Round(a);
                                }
                            }
                            else if (LocalTimeChange.DateTimeToLong(rs2.SynDate) - LocalTimeChange.DateTimeToLong(rs1.SynDate) < TimeSpec(AppContext.TimeSpec))
                            {
                                rs2.PRIMax = rs2.PRIMax / Math.Round(rs2.PRIMax / rs1.PRIMax);
                                rs2.PRIMin = rs2.PRIMin / Math.Round(rs2.PRIMin / rs1.PRIMin);
                            }
                            else if (LocalTimeChange.DateTimeToLong(rs3.SynDate) - LocalTimeChange.DateTimeToLong(rs2.SynDate) < TimeSpec(AppContext.TimeSpec))
                            {
                                rs2.PRIMax = rs2.PRIMax / Math.Round(rs2.PRIMax / rs3.PRIMax);
                                rs2.PRIMin = rs2.PRIMin / Math.Round(rs2.PRIMin / rs3.PRIMin);
                            }
                        }
                        else if (altState1 
                            && Math.Round(rs1.PRIMax * 2) <= Math.Round(rs2.PRIMax) + 1)
                        {
                            if (LocalTimeChange.DateTimeToLong(rs2.SynDate) - LocalTimeChange.DateTimeToLong(rs1.SynDate) < TimeSpec(AppContext.TimeSpec))
                            {
                                rs2.PRIMax = rs2.PRIMax / Math.Round(rs2.PRIMax / rs1.PRIMax);
                                rs2.PRIMin = rs2.PRIMin / Math.Round(rs2.PRIMin / rs1.PRIMin);
                            }
                        }
                        else if (altState3 
                            && Math.Round(rs3.PRIMax * 2) <= Math.Round(rs2.PRIMax) + 1)
                        {
                            if (LocalTimeChange.DateTimeToLong(rs3.SynDate) - LocalTimeChange.DateTimeToLong(rs2.SynDate) < TimeSpec(AppContext.TimeSpec))
                            {
                                rs2.PRIMax = rs2.PRIMax / Math.Round(rs2.PRIMax / rs3.PRIMax);
                                rs2.PRIMin = rs2.PRIMin / Math.Round(rs2.PRIMin / rs3.PRIMin);
                            }
                        }
                    }
                }
            }
        }
        
        private static double CalObliqueDistance(double pri, double pw, int n = 1)
        {
            return ((2 * n - 1) * pri * 75 + pw * 225) / 1000;
        }
        /// <summary>
        /// 推算卫星侧摆角
        /// 1.地球半径6371000米
        /// 2.按照雷达回波波门中心在脉冲间隔中心计算；
        /// </summary>
        /// <param name="h">高度</param>
        /// <param name="pri">重复周期</param>
        /// <param name="pw">脉宽</param>
        /// <param name="pm">脉内带宽</param>
        /// <returns></returns>
        public static List<DeductionObject> CalElevation(double h, double pri, double pw, double pm = 150)
        {
            var m = 5;
            //卫星到地心的距离
            var b = h + Globals.EarthRadius;
            //
            var Smax = Math.Sqrt(b * b - Globals.EarthRadius * Globals.EarthRadius);

            List<DeductionObject> stkList = new List<DeductionObject>();
            // "侦照斜距(Km)\t\t侧视角(deg)\t\tSTK仿真角(deg)"
            for (var n = 1; n <= 200; n++)
            {
                //2S = (n - 1) * pri * c + pw * c + 2 * pw * c + (pri - 3 * pw) / 2 * c
                //S = (n * pri * 300 + ((pri - pw) * 150) + pw * 300) / 2
                var S = CalObliqueDistance(pri, pw, n);
                if (S > h && S < Smax)
                {
                    var ASi = (b * b + S * S - Globals.EarthRadius * Globals.EarthRadius) / (2 * b * S);
                    var BSi = (b * b + Globals.EarthRadius * Globals.EarthRadius - S * S) / (2 * b * Globals.EarthRadius);

                    var jA = Math.Acos(ASi) * 180.0 / Math.PI;
                    var jB = Math.Acos(BSi) * 180.0 / Math.PI;

                    if (jA < 65)
                    {
                        //输出结果
                        DeductionObject deObj = new DeductionObject();
                        deObj.ObliqueDistance = S;
                        deObj.SideAngle = jA;
                        deObj.StkElevation = 90 - jA;
                        deObj.MajorAngle = 90 - jA - jB;
                        var jC = jA + jB;
                        deObj.MajorAngleIn = jC;
                        deObj.HorzResolution = 150 / (pm * Math.Sin(jC * Math.PI / 180.0));
                        deObj.ImageResolution = 150 / pm;
                        stkList.Add(deObj);
                        m = m + 1;
                    }
                }
            }
            return stkList;
        }


        /// <summary>
        /// 通过方位俯仰变化校准一轨中同一秒中的两条数据的顺序
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static List<RadarSignal> SignalJitterCorrection(List<RadarSignal> list)
        {
            if (list.Count > 2)
            {
                long maxPitMills = LocalTimeChange.DateTimeToLong(list.Find(r => r.InterPIT == list.Max(rmax => rmax.InterPIT)).SynDate); // 最高俯仰时间
                List<RadarSignal> rsList = new List<RadarSignal>();
                var groupSynDate = list.GroupBy(r => LocalTimeChange.DateTimeToLong(r.SynDate)); // 时间分组
                int index = 0;
                foreach (var item in groupSynDate)
                {
                    if (item.Count() > 1) // 具有相同时间的
                    {
                        var step = item.Where(r => r.InterAZ > 345 || r.InterAZ < 15).Count() > 1; // 1秒中存在方位跨越0度
                        List<RadarSignal> result;
                        //var prevItem = groupSynDate.ElementAt(index - 1);
                        //var prevAz = prevItem.First().InterAZ;
                        //var curAz = item.First().InterAZ;
                        //double diff = prevAz - curAz;
                        //if (prevAz < 90 && curAz > 270 || diff > 0)
                        //{
                        //    // 方位降序
                        //    result = item.OrderByDescending(r => r.InterAZ).ToList();
                        //}
                        //else if (curAz < 90 && prevAz > 270 || diff < 0)
                        //{
                        //    // 方位升序
                        //    result = item.OrderBy(r => r.InterAZ).ToList();
                        //}
                        if (maxPitMills > item.Key)
                        {
                            // 在最高点之前，俯仰升序   
                            if (step)
                            {
                                Console.Write("存在");
                            }
                            else
                            {
                                
                            }
                            result = item.OrderBy(r => r.InterPIT).ToList();
                        }
                        else if (maxPitMills < item.Key)
                        {// 在最高点之后，俯仰降序
                            if (step)
                            {
                                Console.Write("存在");
                            }
                            else
                            {
                            }
                            result = item.OrderByDescending(r => r.InterPIT).ToList();
                        }
                        else
                        {// 在最高点时
                            result = item.ToList();
                        }
                        rsList.AddRange(result);
                    }
                    else
                    {
                        rsList.AddRange(item);
                    }
                    index++;
                }
                return rsList;
            }
            return list;
        }


        //******** 空气折射率1.0003，中轨卫星高度350-1500，空间损耗32.45 ********

        /// <summary>
        /// 时间指标
        /// </summary>
        /// <param name="spec"></param>
        /// <returns></returns>
        public static double TimeSpec(double spec)
        {
            var log = Math.Log(spec);
            if (double.IsInfinity(log) || double.IsNaN(log))
            {
                log = 0;
            }
            return 1200 * log / 1.0003 + 1500;
        }
        /// <summary>
        /// 重周指标
        /// </summary>
        /// <param name="spec"></param>
        /// <returns></returns>
        public static double PRISpec(double spec)
        {
            var log = Math.Log(spec);
            if (double.IsInfinity(log) || double.IsNaN(log) || log < 0)
            {
                log = 35;
            }
            return 150 * log / 1.0003 - 32.45;
        }

        //********


        /// <summary>
        /// 成像地域屏幕坐标
        /// </summary>
        /// <param name="g1"></param>
        /// <param name="g2"></param>
        /// <param name="w">屏幕宽度</param>
        /// <param name="h">屏幕高度</param>
        /// <param name="angle">侧视角</param>
        /// <param name="l">斜距</param>
        /// <returns></returns>
        public static int[] ImagingXY(GeoTime g1, GeoTime g2, int w, int h, double angle, double l)
        {

            MapPoint mp1 = new MapPoint();
            mp1.X = g1.LongitudeDeg;
            mp1.Y = g1.LatitudeDeg;
            mp1.H = g1.Altitude;
            MapPoint mp1_1 = Translator.LonLatToMercator(mp1);
            MapPoint mp1_2 = Translator.MercatorToScreen(mp1_1, w, h);
            MapPoint mp2 = new MapPoint();
            mp2.X = g2.LongitudeDeg;
            mp2.Y = g2.LatitudeDeg;
            MapPoint mp2_1 = Translator.LonLatToMercator(mp2);
            MapPoint mp2_2 = Translator.MercatorToScreen(mp2_1, w, h);
            
            //double l1 = OrbitRelatedImpl.CalImagingDistance(angle, mp1.H);
            double l2 = Math.Sqrt(mp1.H * mp1.H + l * l - 2 * mp1.H * l * Math.Cos(Globals.ToRadians(angle)));
            double lt = w / Globals.RoadPerimeter * l2;
            double deg = -90;

            int[] x_y = new int[2];
            if (deg > 0)
            {
                double ysub = (mp1_2.Y - mp2_2.Y) / (mp1_2.X - mp2_2.X);
                double degC = 180 - Math.Atan(Math.Abs(ysub)) * 180 / Math.PI - deg;
                x_y[0] = (int)(lt * Math.Cos(degC * Math.PI / 180) + mp1_2.X);
                x_y[1] = (int)(lt * Math.Sin(degC * Math.PI / 180) + mp1_2.Y);
            }
            else
            {
                double ysub = (mp1_2.X - mp2_2.X) / (mp1_2.Y - mp2_2.Y);
                double degC = 180 - Math.Atan(Math.Abs(ysub)) * 180 / Math.PI + deg;
                x_y[0] = (int)(mp1_2.X - lt * Math.Sin(degC * Math.PI / 180));
                x_y[1] = (int)(mp1_2.Y - lt * Math.Cos(degC * Math.PI / 180));
            }

            return x_y;
        }

        /// <summary>
        /// 成像地域经纬度
        /// </summary>
        /// <param name="g1"></param>
        /// <param name="g2"></param>
        /// <param name="angle"></param>
        /// <param name="l"></param>
        /// <returns></returns>
        public static MapPoint ImagingLonLat(GeoTime g1, GeoTime g2, double angle, double l)
        {
            double llf = 360 / Globals.RoadPerimeter;

            var lon1 = g1.LongitudeDeg * 3600;
            var lon2 = g2.LongitudeDeg * 3600;
            var lat1 = g1.LatitudeDeg * 3600;
            var lat2 = g2.LatitudeDeg * 3600;

            double lt = Math.Sqrt(g1.Altitude * g1.Altitude + l * l - 2 * g1.Altitude * l * Math.Cos(Globals.ToRadians(angle)));
            double l2 = lt * llf;
            double deg = -90;
            
            MapPoint lon_lat = new MapPoint();
            double ysub = (lat1 - lat2) / (lon1 - lon2);
            double degC = 180 - Math.Atan(Math.Abs(ysub)) * 180 / Math.PI - deg;

            if (deg > 0)
            {
                lon_lat.X = l2 * Math.Cos(degC * Math.PI / 180) + lon1;
                lon_lat.Y = l2 * Math.Sin(degC * Math.PI / 180) - lat1;
            }
            else
            {
                lon_lat.X = lon1 - l2 * Math.Sin(degC * Math.PI / 180);
                lon_lat.Y = lat1 - l2 * Math.Cos(degC * Math.PI / 180);
            }

            lon_lat.X = lon_lat.X / 3600;
            lon_lat.Y = lon_lat.Y / 3600;

            return lon_lat;
        }


        /// <summary>
        /// for循环数据输出百分比进度
        /// </summary>
        /// <param name="outup">百分比值</param>
        /// <param name="forLen">for长度</param>
        /// <param name="index">for当前索引</param>
        /// <param name="pv">进度分度</param>
        /// <param name="minPv">最小进度</param>
        /// <param name="endPv">最大进度</param>
        /// <returns></returns>
        public static bool ForPercentageValue(out int outup, int forLen, int index, int pv = 1, int minPv = 0, int maxPv = 100)
        {
            // 分度大小
            double scale = Math.Ceiling(forLen * 1.0 / (maxPv - minPv) * pv);

            // 当前索引对于分度大小的倍数
            int n = (int)Math.Ceiling(index / scale);

            if (index == (int)scale * n)
            {
                outup = n * pv + minPv;
                return true;
            }
            else if (index + 1 == forLen)
            {
                outup = maxPv;
                return true;
            }

            outup = 0;
            return false;
        }


        /// <summary>
        /// 标准差
        /// </summary>
        /// <param name="arr"></param>
        /// <returns></returns>
        public static double CalculateStdDev(IEnumerable<double> arr)
        {
            double xSum = 0; // 样本总和
            double xAvg = 0; // 样本平均值
            double sSum = 0; // 方差的分子

            double std_dev = 0;
            var arrLen = arr.Count();
            if (arrLen > 0)
            {
                xSum = arr.Sum();
                xAvg = xSum / arrLen;
                sSum = arr.Sum(s => (s - xAvg) * (s - xAvg));
                std_dev = Math.Sqrt((sSum / (arrLen - 1)));
            }
            return std_dev;
        }
        

    }


    /// <summary>
    /// STK仿真角
    /// </summary>
    public class DeductionObject
    {
        public double ObliqueDistance { set; get; } // 侦照斜距KM
        public double SideAngle { set; get; }  // 侧视角
        public double StkElevation { set; get; } // STK仿真角

        public double MajorAngle { set; get; } // 主瓣地域仰角
        public double MajorAngleIn { set; get; } // 主瓣地域入射角
        public double HorzResolution { set; get; } // 地距分辨率
        public double ImageResolution { set; get; } // 斜距分辨率

    }

}
