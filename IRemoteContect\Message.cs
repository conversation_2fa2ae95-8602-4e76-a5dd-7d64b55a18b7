﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace IRemoteContect
{
    public interface Message
    {
        void SendMessage(string message);

        void PostCommand(string key, params object[] values);

        event SendHandler SendEvent;
        event ReceiveHandler ReceiveEvent;
    }

    /// <summary>
    /// 客户端发送信息
    /// </summary>
    /// <param name="info"></param>
    public delegate void SendHandler(object info);

    /// <summary>
    /// 客户端接收信息
    /// </summary>
    /// <param name="info"></param>
    public delegate void ReceiveHandler(object info);

    public class SwapData : MarshalByRefObject
    {
        /// <summary>
        /// 服务器触发，客户端订阅事件
        /// </summary>
        private ReceiveHandler SwapDataObject;
        public event ReceiveHandler ReceiveEvent
        {
            add
            {
                if (SwapDataObject == null)
                {
                    SwapDataObject += value;
                }
            }
            remove
            {
                SwapDataObject -= value;
            }
        }

        /// <summary>
        /// 触发事件
        /// </summary>
        /// <param name="data"></param>
        public virtual void TriggerSwapData(object data)
        {
            SwapDataObject?.Invoke(data);
        }

        /// <summary>
        /// 该类处于无限生命周期
        /// </summary>
        /// <returns></returns>
        public override object InitializeLifetimeService()
        {
            return null;
        }
    }
}
