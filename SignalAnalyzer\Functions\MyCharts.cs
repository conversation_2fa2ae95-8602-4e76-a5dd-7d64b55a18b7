﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms.DataVisualization.Charting;

namespace SignalAnalyzer.Functions
{
    public class MyCharts
    {
        private Chart chart;
        private Series series;


        public void Init(Chart chart)
        {
            this.chart = chart;
            series = chart.Series[0];
            series.MarkerSize = 8;
            series.MarkerStyle = MarkerStyle.Circle;
            series.MarkerColor = System.Drawing.Color.CadetBlue;
            series.IsValueShownAsLabel = true;
            //chart.GetToolTipText += Chart_GetToolTipText;
            chart.MouseMove += Chart_MouseMove;
            chart.ChartAreas[0].CursorX.LineColor = System.Drawing.Color.Silver;
        }

        private void Chart_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            var xVal = chart.ChartAreas[0].AxisX.PixelPositionToValue(e.X);
            var yVal = chart.ChartAreas[0].AxisY.PixelPositionToValue(e.Y);
            if (xVal != -1 && xVal < chart.ChartAreas[0].AxisX.Maximum && yVal != -1 && yVal < chart.ChartAreas[0].AxisY.Maximum)
            {
                chart.ChartAreas[0].CursorX.Position = xVal;
                //chart.ChartAreas[0].CursorY.Position = yVal;
            }
        }

        private void Chart_GetToolTipText(object sender, ToolTipEventArgs e)
        {
            HitTestResult result = chart.HitTest(e.X, e.Y); //获取命中的测试结果
            if(result.ChartElementType == ChartElementType.DataPoint)
            {
                var i = result.PointIndex;
                var dp = result.Series.Points[i];
                var yValue = dp.YValues[0];
                e.Text = $"{ yValue :f2}";
            }
        }

        public void SetValue(double[] xVal, double[] yVal)
        {
            if (xVal.Length > 10)
            {
                series.IsValueShownAsLabel = false;
            }
            series.Points.DataBindXY(xVal, yVal);
        }
    }
}
