﻿namespace OrbitPredictor
{
    partial class OrbitMain
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(OrbitMain));
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.panel3 = new System.Windows.Forms.Panel();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.forecastDataView = new System.Windows.Forms.DataGridView();
            this.menuDataOption = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.etlOrbit = new System.Windows.Forms.ToolStripMenuItem();
            this.exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.panel2 = new System.Windows.Forms.Panel();
            this.tbAlt = new System.Windows.Forms.TextBox();
            this.tbLon = new System.Windows.Forms.TextBox();
            this.tbLat = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.btnForecast = new System.Windows.Forms.Button();
            this.btnSelect = new System.Windows.Forms.Button();
            this.btnOpenTle = new System.Windows.Forms.Button();
            this.btnReset = new System.Windows.Forms.Button();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.checkBox3 = new System.Windows.Forms.CheckBox();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.rangeDel = new System.Windows.Forms.Button();
            this.rangeToList = new System.Windows.Forms.Button();
            this.skipRangeList = new System.Windows.Forms.ListBox();
            this.cbRgCloseR = new System.Windows.Forms.CheckBox();
            this.cbRgCloseL = new System.Windows.Forms.CheckBox();
            this.cbUseUTC = new System.Windows.Forms.CheckBox();
            this.cbSkipTime = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.nicSatEleMax = new System.Windows.Forms.NumericUpDown();
            this.nicSatEleMin = new System.Windows.Forms.NumericUpDown();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.progressBarDisplay = new System.Windows.Forms.ToolStripProgressBar();
            this.percentageValue = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolSynTime = new System.Windows.Forms.ToolStripStatusLabel();
            this.coordinateText = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel4 = new System.Windows.Forms.ToolStripStatusLabel();
            this.appLog = new System.Windows.Forms.TextBox();
            this.mapPanel = new OrbitPredictor.PanelEnhanced();
            this.timeEnd = new OrbitPredictor.Controls.DateTimeInputBox();
            this.timeStart = new OrbitPredictor.Controls.DateTimeInputBox();
            this.timeRangeBox = new OrbitPredictor.Controls.TimeInputBox();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.forecastDataView)).BeginInit();
            this.menuDataOption.SuspendLayout();
            this.panel2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nicSatEleMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nicSatEleMin)).BeginInit();
            this.statusStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(4);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1008, 527);
            this.tabControl1.TabIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.BackColor = System.Drawing.Color.Black;
            this.tabPage1.Controls.Add(this.mapPanel);
            this.tabPage1.Controls.Add(this.panel3);
            this.tabPage1.Location = new System.Drawing.Point(4, 25);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(1000, 498);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "态势显示";
            this.tabPage1.Resize += new System.EventHandler(this.tabPage1_Resize);
            // 
            // panel3
            // 
            this.panel3.BackColor = System.Drawing.Color.White;
            this.panel3.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel3.Location = new System.Drawing.Point(743, 0);
            this.panel3.Margin = new System.Windows.Forms.Padding(4);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(257, 498);
            this.panel3.TabIndex = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.BackColor = System.Drawing.Color.White;
            this.tabPage2.Controls.Add(this.forecastDataView);
            this.tabPage2.Controls.Add(this.panel2);
            this.tabPage2.Location = new System.Drawing.Point(4, 25);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage2.Size = new System.Drawing.Size(1000, 498);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "轨道预测";
            // 
            // forecastDataView
            // 
            this.forecastDataView.AllowUserToAddRows = false;
            this.forecastDataView.AllowUserToDeleteRows = false;
            this.forecastDataView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.forecastDataView.ColumnHeadersHeight = 32;
            this.forecastDataView.ContextMenuStrip = this.menuDataOption;
            this.forecastDataView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.forecastDataView.Location = new System.Drawing.Point(4, 105);
            this.forecastDataView.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.forecastDataView.MultiSelect = false;
            this.forecastDataView.Name = "forecastDataView";
            this.forecastDataView.ReadOnly = true;
            this.forecastDataView.RowTemplate.Height = 27;
            this.forecastDataView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.forecastDataView.Size = new System.Drawing.Size(992, 389);
            this.forecastDataView.TabIndex = 8;
            this.forecastDataView.VirtualMode = true;
            this.forecastDataView.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.forecastDataView_CellFormatting);
            this.forecastDataView.CellMouseDown += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.forecastDataView_CellMouseDown);
            // 
            // menuDataOption
            // 
            this.menuDataOption.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.etlOrbit,
            this.exportExcel});
            this.menuDataOption.Name = "menuDataOption";
            this.menuDataOption.Size = new System.Drawing.Size(141, 48);
            this.menuDataOption.Opening += new System.ComponentModel.CancelEventHandler(this.menuDataOption_Opening);
            // 
            // etlOrbit
            // 
            this.etlOrbit.Font = new System.Drawing.Font("宋体", 12F);
            this.etlOrbit.Name = "etlOrbit";
            this.etlOrbit.Size = new System.Drawing.Size(140, 22);
            this.etlOrbit.Text = "加载轨道";
            this.etlOrbit.Click += new System.EventHandler(this.etlOrbit_Click);
            // 
            // exportExcel
            // 
            this.exportExcel.Font = new System.Drawing.Font("宋体", 12F);
            this.exportExcel.Name = "exportExcel";
            this.exportExcel.Size = new System.Drawing.Size(140, 22);
            this.exportExcel.Text = "导出CSV";
            this.exportExcel.Click += new System.EventHandler(this.exportExcel_Click);
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.timeEnd);
            this.panel2.Controls.Add(this.timeStart);
            this.panel2.Controls.Add(this.tbAlt);
            this.panel2.Controls.Add(this.tbLon);
            this.panel2.Controls.Add(this.tbLat);
            this.panel2.Controls.Add(this.label1);
            this.panel2.Controls.Add(this.label5);
            this.panel2.Controls.Add(this.label6);
            this.panel2.Controls.Add(this.btnForecast);
            this.panel2.Controls.Add(this.btnSelect);
            this.panel2.Controls.Add(this.btnOpenTle);
            this.panel2.Controls.Add(this.btnReset);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2.Location = new System.Drawing.Point(4, 4);
            this.panel2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(992, 101);
            this.panel2.TabIndex = 0;
            // 
            // tbAlt
            // 
            this.tbAlt.Location = new System.Drawing.Point(345, 61);
            this.tbAlt.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tbAlt.MaxLength = 8;
            this.tbAlt.Name = "tbAlt";
            this.tbAlt.Size = new System.Drawing.Size(100, 24);
            this.tbAlt.TabIndex = 19;
            // 
            // tbLon
            // 
            this.tbLon.Location = new System.Drawing.Point(239, 61);
            this.tbLon.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tbLon.MaxLength = 8;
            this.tbLon.Name = "tbLon";
            this.tbLon.Size = new System.Drawing.Size(100, 24);
            this.tbLon.TabIndex = 18;
            // 
            // tbLat
            // 
            this.tbLat.Location = new System.Drawing.Point(133, 61);
            this.tbLat.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tbLat.MaxLength = 8;
            this.tbLat.Name = "tbLat";
            this.tbLat.Size = new System.Drawing.Size(100, 24);
            this.tbLat.TabIndex = 17;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.Color.Transparent;
            this.label1.Location = new System.Drawing.Point(372, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(82, 15);
            this.label1.TabIndex = 0;
            this.label1.Text = "结束时间：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.BackColor = System.Drawing.Color.Transparent;
            this.label5.Location = new System.Drawing.Point(15, 66);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(108, 15);
            this.label5.TabIndex = 0;
            this.label5.Text = "站点(E/N/m)：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.BackColor = System.Drawing.Color.Transparent;
            this.label6.Location = new System.Drawing.Point(15, 24);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(82, 15);
            this.label6.TabIndex = 0;
            this.label6.Text = "开始时间：";
            // 
            // btnForecast
            // 
            this.btnForecast.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnForecast.Location = new System.Drawing.Point(864, 54);
            this.btnForecast.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnForecast.Name = "btnForecast";
            this.btnForecast.Size = new System.Drawing.Size(120, 38);
            this.btnForecast.TabIndex = 20;
            this.btnForecast.Text = "3.目标预报";
            this.btnForecast.UseVisualStyleBackColor = true;
            this.btnForecast.Click += new System.EventHandler(this.btnForecast_Click);
            // 
            // btnSelect
            // 
            this.btnSelect.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSelect.Location = new System.Drawing.Point(738, 54);
            this.btnSelect.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnSelect.Name = "btnSelect";
            this.btnSelect.Size = new System.Drawing.Size(120, 38);
            this.btnSelect.TabIndex = 14;
            this.btnSelect.Text = "2.选择目标";
            this.btnSelect.UseVisualStyleBackColor = true;
            this.btnSelect.Click += new System.EventHandler(this.btnSelect_Click);
            // 
            // btnOpenTle
            // 
            this.btnOpenTle.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOpenTle.Location = new System.Drawing.Point(864, 12);
            this.btnOpenTle.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnOpenTle.Name = "btnOpenTle";
            this.btnOpenTle.Size = new System.Drawing.Size(120, 38);
            this.btnOpenTle.TabIndex = 13;
            this.btnOpenTle.Text = "1.打开TLE";
            this.btnOpenTle.UseVisualStyleBackColor = true;
            this.btnOpenTle.Click += new System.EventHandler(this.btnOpenTle_Click);
            // 
            // btnReset
            // 
            this.btnReset.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnReset.Location = new System.Drawing.Point(738, 12);
            this.btnReset.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(120, 38);
            this.btnReset.TabIndex = 21;
            this.btnReset.Text = "0.重置";
            this.btnReset.UseVisualStyleBackColor = true;
            this.btnReset.Click += new System.EventHandler(this.btnReset_Click);
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.groupBox3);
            this.tabPage3.Controls.Add(this.groupBox2);
            this.tabPage3.Controls.Add(this.groupBox1);
            this.tabPage3.Location = new System.Drawing.Point(4, 25);
            this.tabPage3.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage3.Size = new System.Drawing.Size(1000, 498);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "预测设定";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.checkBox3);
            this.groupBox3.Controls.Add(this.checkBox2);
            this.groupBox3.Location = new System.Drawing.Point(23, 125);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox3.Size = new System.Drawing.Size(491, 123);
            this.groupBox3.TabIndex = 12;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "显示";
            // 
            // checkBox3
            // 
            this.checkBox3.AutoSize = true;
            this.checkBox3.Location = new System.Drawing.Point(20, 56);
            this.checkBox3.Margin = new System.Windows.Forms.Padding(4);
            this.checkBox3.Name = "checkBox3";
            this.checkBox3.Size = new System.Drawing.Size(116, 19);
            this.checkBox3.TabIndex = 12;
            this.checkBox3.Text = "显示经纬网格";
            this.checkBox3.UseVisualStyleBackColor = true;
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Location = new System.Drawing.Point(20, 29);
            this.checkBox2.Margin = new System.Windows.Forms.Padding(4);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(116, 19);
            this.checkBox2.TabIndex = 11;
            this.checkBox2.Text = "显示夜间阴影";
            this.checkBox2.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.rangeDel);
            this.groupBox2.Controls.Add(this.rangeToList);
            this.groupBox2.Controls.Add(this.skipRangeList);
            this.groupBox2.Controls.Add(this.timeRangeBox);
            this.groupBox2.Controls.Add(this.cbRgCloseR);
            this.groupBox2.Controls.Add(this.cbRgCloseL);
            this.groupBox2.Controls.Add(this.cbUseUTC);
            this.groupBox2.Controls.Add(this.cbSkipTime);
            this.groupBox2.Location = new System.Drawing.Point(537, 22);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox2.Size = new System.Drawing.Size(442, 369);
            this.groupBox2.TabIndex = 12;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "设定";
            // 
            // rangeDel
            // 
            this.rangeDel.Location = new System.Drawing.Point(232, 51);
            this.rangeDel.Margin = new System.Windows.Forms.Padding(4);
            this.rangeDel.Name = "rangeDel";
            this.rangeDel.Size = new System.Drawing.Size(64, 29);
            this.rangeDel.TabIndex = 4;
            this.rangeDel.Text = "删除";
            this.rangeDel.UseVisualStyleBackColor = true;
            this.rangeDel.Click += new System.EventHandler(this.rangeDel_Click);
            // 
            // rangeToList
            // 
            this.rangeToList.Location = new System.Drawing.Point(160, 51);
            this.rangeToList.Margin = new System.Windows.Forms.Padding(4);
            this.rangeToList.Name = "rangeToList";
            this.rangeToList.Size = new System.Drawing.Size(64, 29);
            this.rangeToList.TabIndex = 3;
            this.rangeToList.Text = "添加";
            this.rangeToList.UseVisualStyleBackColor = true;
            this.rangeToList.Click += new System.EventHandler(this.rangeToList_Click);
            // 
            // skipRangeList
            // 
            this.skipRangeList.Font = new System.Drawing.Font("宋体", 11F);
            this.skipRangeList.FormattingEnabled = true;
            this.skipRangeList.ItemHeight = 15;
            this.skipRangeList.Location = new System.Drawing.Point(59, 120);
            this.skipRangeList.Margin = new System.Windows.Forms.Padding(4);
            this.skipRangeList.Name = "skipRangeList";
            this.skipRangeList.Size = new System.Drawing.Size(237, 154);
            this.skipRangeList.TabIndex = 6;
            // 
            // cbRgCloseR
            // 
            this.cbRgCloseR.AutoSize = true;
            this.cbRgCloseR.Location = new System.Drawing.Point(20, 321);
            this.cbRgCloseR.Margin = new System.Windows.Forms.Padding(4);
            this.cbRgCloseR.Name = "cbRgCloseR";
            this.cbRgCloseR.Size = new System.Drawing.Size(161, 19);
            this.cbRgCloseR.TabIndex = 8;
            this.cbRgCloseR.Text = "预报区间闭环（右）";
            this.cbRgCloseR.UseVisualStyleBackColor = true;
            this.cbRgCloseR.CheckedChanged += new System.EventHandler(this.cbRgCloseR_CheckedChanged);
            // 
            // cbRgCloseL
            // 
            this.cbRgCloseL.AutoSize = true;
            this.cbRgCloseL.Location = new System.Drawing.Point(20, 294);
            this.cbRgCloseL.Margin = new System.Windows.Forms.Padding(4);
            this.cbRgCloseL.Name = "cbRgCloseL";
            this.cbRgCloseL.Size = new System.Drawing.Size(161, 19);
            this.cbRgCloseL.TabIndex = 7;
            this.cbRgCloseL.Text = "预报区间闭环（左）";
            this.cbRgCloseL.UseVisualStyleBackColor = true;
            this.cbRgCloseL.CheckedChanged += new System.EventHandler(this.cbRgCloseL_CheckedChanged);
            // 
            // cbUseUTC
            // 
            this.cbUseUTC.AutoSize = true;
            this.cbUseUTC.Location = new System.Drawing.Point(20, 29);
            this.cbUseUTC.Margin = new System.Windows.Forms.Padding(4);
            this.cbUseUTC.Name = "cbUseUTC";
            this.cbUseUTC.Size = new System.Drawing.Size(110, 19);
            this.cbUseUTC.TabIndex = 1;
            this.cbUseUTC.Text = "使用UTC时间";
            this.cbUseUTC.UseVisualStyleBackColor = true;
            this.cbUseUTC.CheckedChanged += new System.EventHandler(this.cbUseUTC_CheckedChanged);
            // 
            // cbSkipTime
            // 
            this.cbSkipTime.AutoSize = true;
            this.cbSkipTime.Location = new System.Drawing.Point(20, 56);
            this.cbSkipTime.Margin = new System.Windows.Forms.Padding(4);
            this.cbSkipTime.Name = "cbSkipTime";
            this.cbSkipTime.Size = new System.Drawing.Size(131, 19);
            this.cbSkipTime.TabIndex = 2;
            this.cbSkipTime.Text = "跳过指定时间段";
            this.cbSkipTime.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.nicSatEleMax);
            this.groupBox1.Controls.Add(this.nicSatEleMin);
            this.groupBox1.Location = new System.Drawing.Point(23, 22);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(4);
            this.groupBox1.Size = new System.Drawing.Size(491, 95);
            this.groupBox1.TabIndex = 11;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "状态";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.BackColor = System.Drawing.Color.Transparent;
            this.label2.Location = new System.Drawing.Point(236, 29);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(15, 15);
            this.label2.TabIndex = 9;
            this.label2.Text = ")";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.BackColor = System.Drawing.Color.Transparent;
            this.label7.Location = new System.Drawing.Point(20, 29);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(82, 15);
            this.label7.TabIndex = 9;
            this.label7.Text = "卫星仰角（";
            // 
            // nicSatEleMax
            // 
            this.nicSatEleMax.Location = new System.Drawing.Point(170, 24);
            this.nicSatEleMax.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.nicSatEleMax.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nicSatEleMax.Name = "nicSatEleMax";
            this.nicSatEleMax.Size = new System.Drawing.Size(60, 24);
            this.nicSatEleMax.TabIndex = 10;
            this.nicSatEleMax.Value = new decimal(new int[] {
            90,
            0,
            0,
            0});
            // 
            // nicSatEleMin
            // 
            this.nicSatEleMin.Location = new System.Drawing.Point(104, 24);
            this.nicSatEleMin.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.nicSatEleMin.Maximum = new decimal(new int[] {
            360,
            0,
            0,
            0});
            this.nicSatEleMin.Name = "nicSatEleMin";
            this.nicSatEleMin.Size = new System.Drawing.Size(60, 24);
            this.nicSatEleMin.TabIndex = 9;
            // 
            // statusStrip1
            // 
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.progressBarDisplay,
            this.percentageValue,
            this.toolStripStatusLabel1,
            this.toolSynTime,
            this.coordinateText,
            this.toolStripStatusLabel4});
            this.statusStrip1.Location = new System.Drawing.Point(0, 667);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(1, 0, 19, 0);
            this.statusStrip1.Size = new System.Drawing.Size(1008, 34);
            this.statusStrip1.TabIndex = 3;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // progressBarDisplay
            // 
            this.progressBarDisplay.Name = "progressBarDisplay";
            this.progressBarDisplay.Size = new System.Drawing.Size(133, 28);
            // 
            // percentageValue
            // 
            this.percentageValue.Name = "percentageValue";
            this.percentageValue.Size = new System.Drawing.Size(26, 29);
            this.percentageValue.Text = "0%";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.AutoSize = false;
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(100, 29);
            this.toolStripStatusLabel1.Text = "欢迎使用";
            // 
            // toolSynTime
            // 
            this.toolSynTime.BorderSides = ((System.Windows.Forms.ToolStripStatusLabelBorderSides)((System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right)));
            this.toolSynTime.Name = "toolSynTime";
            this.toolSynTime.Size = new System.Drawing.Size(551, 29);
            this.toolSynTime.Spring = true;
            this.toolSynTime.Text = "00:00:00.0";
            // 
            // coordinateText
            // 
            this.coordinateText.AutoSize = false;
            this.coordinateText.Name = "coordinateText";
            this.coordinateText.Size = new System.Drawing.Size(160, 29);
            this.coordinateText.Text = "0,0";
            this.coordinateText.ToolTipText = "坐标（经度，纬度）";
            // 
            // toolStripStatusLabel4
            // 
            this.toolStripStatusLabel4.Name = "toolStripStatusLabel4";
            this.toolStripStatusLabel4.Size = new System.Drawing.Size(16, 29);
            this.toolStripStatusLabel4.Text = "  ";
            // 
            // appLog
            // 
            this.appLog.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.appLog.Location = new System.Drawing.Point(0, 527);
            this.appLog.Margin = new System.Windows.Forms.Padding(4);
            this.appLog.Multiline = true;
            this.appLog.Name = "appLog";
            this.appLog.ReadOnly = true;
            this.appLog.Size = new System.Drawing.Size(1008, 140);
            this.appLog.TabIndex = 2;
            // 
            // mapPanel
            // 
            this.mapPanel.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.mapPanel.Cursor = System.Windows.Forms.Cursors.Cross;
            this.mapPanel.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Default;
            this.mapPanel.Location = new System.Drawing.Point(0, 0);
            this.mapPanel.Margin = new System.Windows.Forms.Padding(4);
            this.mapPanel.Name = "mapPanel";
            this.mapPanel.Size = new System.Drawing.Size(612, 501);
            this.mapPanel.TabIndex = 1;
            this.mapPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.mapPanel_Paint);
            this.mapPanel.MouseDown += new System.Windows.Forms.MouseEventHandler(this.mapPanel_MouseDown);
            this.mapPanel.MouseMove += new System.Windows.Forms.MouseEventHandler(this.mapPanel_MouseMove);
            this.mapPanel.MouseUp += new System.Windows.Forms.MouseEventHandler(this.mapPanel_MouseUp);
            // 
            // timeEnd
            // 
            this.timeEnd.AutoSize = true;
            this.timeEnd.BackColor = System.Drawing.Color.White;
            this.timeEnd.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.timeEnd.DateTimeValue = new System.DateTime(2010, 1, 1, 0, 0, 0, 0);
            this.timeEnd.Font = new System.Drawing.Font("宋体", 12F);
            this.timeEnd.Location = new System.Drawing.Point(467, 18);
            this.timeEnd.Margin = new System.Windows.Forms.Padding(4);
            this.timeEnd.MinimumSize = new System.Drawing.Size(178, 28);
            this.timeEnd.Name = "timeEnd";
            this.timeEnd.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeEnd.Size = new System.Drawing.Size(238, 28);
            this.timeEnd.TabIndex = 16;
            // 
            // timeStart
            // 
            this.timeStart.AutoSize = true;
            this.timeStart.BackColor = System.Drawing.Color.White;
            this.timeStart.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.timeStart.DateTimeValue = new System.DateTime(2010, 1, 1, 0, 0, 0, 0);
            this.timeStart.Font = new System.Drawing.Font("宋体", 12F);
            this.timeStart.Location = new System.Drawing.Point(110, 18);
            this.timeStart.Margin = new System.Windows.Forms.Padding(4);
            this.timeStart.MinimumSize = new System.Drawing.Size(178, 28);
            this.timeStart.Name = "timeStart";
            this.timeStart.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeStart.Size = new System.Drawing.Size(238, 28);
            this.timeStart.TabIndex = 15;
            // 
            // timeRangeBox
            // 
            this.timeRangeBox.AutoSize = true;
            this.timeRangeBox.BackColor = System.Drawing.Color.White;
            this.timeRangeBox.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.timeRangeBox.Location = new System.Drawing.Point(59, 86);
            this.timeRangeBox.Margin = new System.Windows.Forms.Padding(4, 2, 4, 2);
            this.timeRangeBox.MinimumSize = new System.Drawing.Size(233, 19);
            this.timeRangeBox.Name = "timeRangeBox";
            this.timeRangeBox.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timeRangeBox.Size = new System.Drawing.Size(237, 23);
            this.timeRangeBox.TabIndex = 5;
            // 
            // OrbitMain
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 701);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.appLog);
            this.Controls.Add(this.statusStrip1);
            this.Font = new System.Drawing.Font("宋体", 11F);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MinimumSize = new System.Drawing.Size(1024, 739);
            this.Name = "OrbitMain";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "卫星轨道预报";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.OrbitMain_FormClosing);
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.OrbitMain_FormClosed);
            this.Load += new System.EventHandler(this.OrbitMain_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.forecastDataView)).EndInit();
            this.menuDataOption.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nicSatEleMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nicSatEleMin)).EndInit();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.DataGridView forecastDataView;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.TextBox tbAlt;
        private System.Windows.Forms.TextBox tbLon;
        private System.Windows.Forms.TextBox tbLat;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Button btnForecast;
        private System.Windows.Forms.Button btnSelect;
        private System.Windows.Forms.Button btnOpenTle;
        private System.Windows.Forms.Button btnReset;
        private Controls.DateTimeInputBox timeEnd;
        private Controls.DateTimeInputBox timeStart;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.CheckBox cbUseUTC;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown nicSatEleMin;
        private System.Windows.Forms.CheckBox cbSkipTime;
        private System.Windows.Forms.CheckBox checkBox3;
        private System.Windows.Forms.CheckBox checkBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Panel panel3;
        private PanelEnhanced mapPanel;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripProgressBar progressBarDisplay;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel toolSynTime;
        private System.Windows.Forms.ToolStripStatusLabel coordinateText;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel4;
        private System.Windows.Forms.ContextMenuStrip menuDataOption;
        private System.Windows.Forms.ToolStripMenuItem etlOrbit;
        private System.Windows.Forms.TextBox appLog;
        private Controls.TimeInputBox timeRangeBox;
        private System.Windows.Forms.Button rangeToList;
        private System.Windows.Forms.ListBox skipRangeList;
        private System.Windows.Forms.Button rangeDel;
        private System.Windows.Forms.ToolStripStatusLabel percentageValue;
        private System.Windows.Forms.CheckBox cbRgCloseL;
        private System.Windows.Forms.CheckBox cbRgCloseR;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown nicSatEleMax;
        private System.Windows.Forms.ToolStripMenuItem exportExcel;
    }
}

