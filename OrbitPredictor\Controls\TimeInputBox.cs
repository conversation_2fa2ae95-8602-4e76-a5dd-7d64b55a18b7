﻿using System;
using System.ComponentModel;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace OrbitPredictor.Controls
{
    public partial class TimeInputBox : UserControl
    {
        private Control[] thisTextBox;

        public TimeInputBox()
        {
            InitializeComponent();
            thisTextBox = new Control[] { this.HH1, this.mm1, this.ss1, this.HH2, this.mm2, this.ss2 };
            foreach (Control c in this.Controls)
            {
                if (c.GetType().Equals(typeof(TextBoxBottomBorder)))
                {
                    c.Enter += new EventHandler(c_Enter);
                    c.KeyPress += new KeyPressEventHandler(c_KeyPress);
                    c.TextChanged += new EventHandler(c_TextChanged);
                    c.Validating += new CancelEventHandler(c_Validating);
                }
            }
        }

        /// <summary>
        /// 当文本输入到最大字数时跳到下个输入框
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void c_TextChanged(object sender, EventArgs e)
        {
            if (thisTextBox != null && ((TextBox)sender).TextLength == ((TextBox)sender).MaxLength)
            {
                for (var i = 0; i < thisTextBox.Length - 1; i++)
                {
                    if (sender == thisTextBox[i])
                    {
                        thisTextBox[i + 1].Focus();
                    }
                }
            }
        }


        /// <summary>
        /// 控件激活后前面没有填，把焦点放到前面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void c_Enter(object sender, EventArgs e)
        {
            if (thisTextBox != null)
            {
                for (var i = 1; i < thisTextBox.Length; i++)
                {
                    if (sender == thisTextBox[i])
                    {
                        if (string.IsNullOrWhiteSpace(thisTextBox[i - 1].Text))
                        {
                            thisTextBox[i - 1].Focus();
                        }
                    }
                }
            }
        }


        void c_Validating(object sender, CancelEventArgs e)
        {
            const string pattern = @"^\d{1}";
            string content = ((TextBox)sender).Text;
            if (!string.IsNullOrWhiteSpace(content))
            {
                if (!Regex.IsMatch(content, pattern))
                {
                    ((TextBox)sender).Clear();
                    //((TextBox)sender).Focus();
                    e.Cancel = true;
                }
            }
        }

        /// <summary>
        /// 只允许输入数字或退格键
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void c_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar != 8 && !Char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
            if (e.KeyChar == 8)
            {
                //输入退格键后如果文本框没有值跳到上一个输入框
                if (thisTextBox != null && ((TextBox)sender).TextLength == 0)
                {
                    for (var i = 1; i < thisTextBox.Length; i++)
                    {
                        if (sender == thisTextBox[i])
                        {
                            thisTextBox[i - 1].Focus();
                        }
                    }
                }
            }
        }

        private void TimeInputBox_BackColorChanged(object sender, EventArgs e)
        {
            foreach (Control c in this.Controls)
            {
                c.BackColor = this.BackColor;
            }
        }

        private void TimeInputBox_FontChanged(object sender, EventArgs e)
        {
            var width = 4;
            var height = 0;
            foreach (Control c in this.Controls)
            {
                c.Font = this.Font;
                width += c.Width;
                height = c.Height > height ? c.Height : height;
            }
            this.MinimumSize = new System.Drawing.Size(width, height + 2);
            this.Size = new System.Drawing.Size(width + 2, height + 6);
        }
        
        [Browsable(false)]
        public TimeSpan[] TimeRangeValue
        {
            get
            {
                TimeSpan[] timeRange = new TimeSpan[2];
                timeRange[0] = new TimeSpan(Convert.ToInt16(this.HH1.Text), Convert.ToInt16(this.mm1.Text), Convert.ToInt16(this.ss1.Text));
                timeRange[1] = new TimeSpan(Convert.ToInt16(this.HH2.Text), Convert.ToInt16(this.mm2.Text), Convert.ToInt16(this.ss2.Text));
                return timeRange;
            }
        }

        public override string ToString()
        {
            if (this.HH1.Text.Trim().Length > 0 && this.mm1.Text.Trim().Length > 0 && this.ss1.Text.Trim().Length > 0 && this.HH2.Text.Trim().Length > 0 && this.mm2.Text.Trim().Length > 0 && this.ss2.Text.Trim().Length > 0)
                return $"{ this.HH1.Text }:{ this.mm1.Text }:{ this.ss1.Text }-{ this.HH2.Text }:{ this.mm2.Text }:{ this.ss2.Text }";
            return "00:00:00-00:00:00";
        }

        public bool Verification()
        {
            return !(GetEndSecond() < GetStartSecond());
        }

        public int GetStartSecond()
        {
            var h1 = Convert.ToInt16(this.HH1.Text);
            var m1 = Convert.ToInt16(this.mm1.Text);
            var s1 = Convert.ToInt16(this.ss1.Text);
            var span = new TimeSpan(h1, m1, s1).TotalSeconds;
            return (int)span;
        }


        public int GetEndSecond()
        {
            var h2 = Convert.ToInt16(this.HH2.Text);
            var m2 = Convert.ToInt16(this.mm2.Text);
            var s2 = Convert.ToInt16(this.ss2.Text);
            var span = new TimeSpan(h2, m2, s2).TotalSeconds;
            return (int)span;
        }

    }
}
